import { useMutation, useQuery } from '@tanstack/react-query';
import { openai } from '../config/openai';
import type { ChatCompletionRequest } from '../config/openai';
import useAuth from './useAuth';
import type { UserInfoResponse } from '../types';
import { API_BASE_URL } from '../utils/constants';

// 聊天完成 hook
export const useChatCompletion = () => {
    return useMutation({
        mutationFn: async (request: ChatCompletionRequest) => {
            const response = await openai.chat.completions.create({
                model: request.model || 'deepseek-chat',
                messages: request.messages,
                temperature: request.temperature || 0.2,
                max_tokens: request.max_tokens || 128000,
            });
            return response;
        },
    });
};

// 流式聊天完成 hook
export const useStreamChatCompletion = () => {
    const { token } = useAuth();

    return useMutation({
        mutationFn: async (request: ChatCompletionRequest & {
            onStream?: (chunk: string) => void;
            onComplete?: (fullContent: string) => void;
            onError?: (error: Error) => void;
        }) => {
            try {
                // 构建请求参数
                const requestParams: any = {
                    model: request.model || 'deepseek-ai/DeepSeek-V3',
                    messages: request.messages,
                    temperature: request.temperature || 0.2,
                    max_tokens: request.max_tokens || 128000,
                    stream: true,
                };

                // 如果有 sessionId，也添加到请求中
                if (request.sessionId) {
                    requestParams.sessionId = request.sessionId;
                }

                // 如果有 tools 参数且数组长度大于0，添加到请求中
                if (request.tools && request.tools.length > 0) {
                    requestParams.tools = request.tools;
                }

                // 如果有 knowledgeEnabled 参数，添加到请求中
                if (request.knowledgeEnabled !== undefined) {
                    requestParams.knowledgeEnabled = request.knowledgeEnabled;
                }

                // 如果有 webSearchEnabled 参数，添加到请求中
                if (request.webSearchEnabled !== undefined) {
                    requestParams.webSearchEnabled = request.webSearchEnabled;
                }

                // 使用 fetch API 代替 OpenAI 客户端库 - 修正URL路径
                const headers: Record<string, string> = {
                    'Content-Type': 'application/json',
                };

                // 添加Authorization头
                if (token) {
                    headers['Authorization'] = `Bearer ${token}`;
                }

                const response = await fetch(`${API_BASE_URL}/v1/v1/chat/completions`, {
                    method: 'POST',
                    headers,
                    body: JSON.stringify(requestParams),
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}, statusText: ${response.statusText}`);
                }

                if (!response.body) {
                    throw new Error('Response body is null');
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let fullContent = '';
                let buffer = ''; // 添加缓冲区来处理不完整的数据

                try {
                    while (true) {
                        const { done, value } = await reader.read();

                        if (done) {
                            break;
                        }

                        // 将新数据添加到缓冲区
                        buffer += decoder.decode(value, { stream: true });

                        // 按行分割，但保留最后一个可能不完整的行
                        const lines = buffer.split('\n');

                        // 如果buffer以换行符结尾，最后一个元素会是空字符串
                        // 如果不以换行符结尾，最后一个元素可能是不完整的行
                        if (buffer.endsWith('\n')) {
                            buffer = '';
                        } else {
                            // 保留最后一个可能不完整的行到缓冲区
                            buffer = lines.pop() || '';
                        }

                        // 处理完整的行
                        for (const line of lines) {
                            const trimmedLine = line.trim();

                            if (trimmedLine === '' || trimmedLine === 'data:[DONE]') {
                                continue;
                            }

                            if (trimmedLine.startsWith('data:')) {
                                try {
                                    const jsonStr = trimmedLine.slice(5).trim(); // 移除 'data:' 前缀并修剪空白

                                    // 跳过空的data行
                                    if (!jsonStr) {
                                        continue;
                                    }

                                    const data = JSON.parse(jsonStr);

                                    const content = data.choices?.[0]?.delta?.content || '';
                                    if (content) {
                                        fullContent += content;
                                        request.onStream?.(content);
                                    }

                                    // 检查是否完成
                                    if (data.choices?.[0]?.finish_reason) {
                                        console.log('Stream finished with reason:', data.choices[0].finish_reason);
                                    }
                                } catch (parseError) {
                                    console.warn('解析SSE数据失败:', parseError, 'Line:', trimmedLine);
                                }
                            }
                        }
                    }

                    // 处理缓冲区中剩余的数据
                    if (buffer.trim()) {
                        const trimmedLine = buffer.trim();
                        if (trimmedLine.startsWith('data:')) {
                            try {
                                const jsonStr = trimmedLine.slice(5).trim();
                                if (jsonStr && jsonStr !== '[DONE]') {
                                    const data = JSON.parse(jsonStr);
                                    const content = data.choices?.[0]?.delta?.content || '';
                                    if (content) {
                                        fullContent += content;
                                        request.onStream?.(content);
                                    }
                                }
                            } catch (parseError) {
                                console.warn('解析缓冲区数据失败:', parseError, 'Line:', trimmedLine);
                            }
                        }
                    }
                } finally {
                    reader.releaseLock();
                }

                request.onComplete?.(fullContent);
                return { content: fullContent };
            } catch (error) {
                const err = error as Error;
                console.error('useStreamChatCompletion: 发生错误:', err);
                console.error('useStreamChatCompletion: 错误详情:', {
                    message: err.message,
                    stack: err.stack,
                    name: err.name
                });
                request.onError?.(err);
                throw err;
            }
        },
    });
};

// 获取模型列表 hook
export const useModels = () => {
    return useQuery({
        queryKey: ['openai-models'],
        queryFn: async () => {
            const response = await openai.models.list();
            return response.data;
        },
        staleTime: 60 * 60 * 1000, // 1小时
    });
};

// 文本嵌入 hook
export const useEmbeddings = () => {
    return useMutation({
        mutationFn: async (input: string | string[]) => {
            const response = await openai.embeddings.create({
                model: 'text-embedding-ada-002',
                input: input,
            });
            return response;
        },
    });
};

// 图像生成 hook
export const useImageGeneration = () => {
    return useMutation({
        mutationFn: async (prompt: string, options?: { size?: string; n?: number }) => {
            const response = await openai.images.generate({
                model: 'dall-e-3',
                prompt: prompt,
                size: options?.size as any || '1024x1024',
                n: options?.n || 1,
            });
            return response;
        },
    });
};

// Agent应用相关类型定义
export interface AgentApp {
    id: number;
    name: string;
    description: string;
    avatar?: string;
    createdBy: number;
    isPublic: boolean;
    createdAt: string;
}

export interface AgentAppsResponse {
    code: number;
    message: string;
    data: AgentApp[];
    timestamp: number;
}

// 获取可用Agent应用列表 hook
export const useAgentApps = () => {
    const { token, isAuthenticated } = useAuth();

    return useQuery({
        queryKey: ['agent-apps'],
        queryFn: async (): Promise<AgentAppsResponse> => {
            // 检查认证状态，如果未认证则抛出错误
            if (!isAuthenticated || !token) {
                throw new Error('用户未认证，请重新登录');
            }

            // 使用统一的API工具函数，自动处理token和错误
            const { apiGet } = await import('../utils/api');
            return apiGet<AgentAppsResponse>('/v1/api/apps/available', token);
        },
        enabled: isAuthenticated && !!token, // 只有在认证状态下才启用查询
        staleTime: 5 * 60 * 1000, // 5分钟
        retry: (failureCount, error) => {
            // 如果是认证错误，不进行重试
            if (error.message.includes('登录') || error.message.includes('认证') || error.message.includes('Token已过期')) {
                return false;
            }
            return failureCount < 2;
        },
    });
};

// 获取用户信息 hook
export const useUserInfo = () => {
    const { token, isAuthenticated } = useAuth();

    return useMutation({
        mutationFn: async (): Promise<UserInfoResponse> => {
            // 检查认证状态，如果未认证则抛出错误
            if (!isAuthenticated || !token) {
                throw new Error('用户未认证，请重新登录');
            }

            // 使用统一的API工具函数，自动处理token和错误
            const { apiGet } = await import('../utils/api');
            return apiGet<UserInfoResponse>('/v1/api/user/info', token);
        },
    });
}; 