import React, { useState, useEffect, useRef, useCallback } from 'react';
import Masonry, { ResponsiveMasonry } from "react-responsive-masonry";
import TextareaAutosize from 'react-textarea-autosize';
import { Search, Heart, ArrowUp, ImageIcon, Smartphone, Layers, Settings2, HelpCircle, Inbox, Loader2, Trash2, X, CheckCircle, Grid3X3 } from 'lucide-react';
import { generateImage, getMyGeneratedImages, deleteGeneratedImages, editImage, uploadFiles } from '../utils/api';
import type { ImageEditRequest } from '../utils/api';
import { toast } from 'react-hot-toast';
import { useAuth } from '../hooks';
import { AiGeneratedContent } from '../types';
import ImageViewerModal from '../components/ImageViewerModal';

export interface MediaItem {
    id: string;
    type: 'image';
    url: string;
    author: string;
    authorAvatar: string;
    likes: number;
    views: number;
    prompt?: string;
    modelUsed?: string;
    size?: string;
}

// 类型定义
type ColumnCount = typeof COLUMN_OPTIONS[number];
type GenerationCount = typeof GENERATION_COUNT_OPTIONS[number];
type AspectRatio = typeof ASPECT_RATIO_OPTIONS[number]['ratio'];
type PopupType = 'layout' | 'media' | 'ratio' | 'count' | null;
interface LibrarySettings {
    aspectRatio: AspectRatio;
    generationCount: GenerationCount;
    columnCount: ColumnCount;
    mediaType: 'image' | 'video';
}

// 常量定义
const COLUMN_OPTIONS = [3, 4, 5] as const;
const GENERATION_COUNT_OPTIONS = [1, 2, 4] as const;
const ASPECT_RATIO_OPTIONS = [
    { ratio: '1:1', resolution: '1024×1024' },
    { ratio: '16:9', resolution: '1408×768' },
    { ratio: '9:16', resolution: '768×1408' },
    { ratio: '4:3', resolution: '1280×896' },
    { ratio: '3:4', resolution: '896×1280' }
] as const;

const RESPONSIVE_BREAKPOINTS = {
    mobile: 350,
    tablet: 768,
    desktop: 1200
} as const;

// 样式常量
const getButtonStyles = (isDarkMode: boolean, isActive = false) => {
    const baseStyles = 'px-3 py-1.5 text-sm rounded-lg transition-colors';
    const darkStyles = isActive
        ? 'bg-blue-600 text-white hover:bg-blue-700'
        : 'text-gray-400 hover:text-gray-300 hover:bg-gray-700';
    const lightStyles = isActive
        ? 'bg-blue-500 text-white hover:bg-blue-600'
        : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100';

    return `${baseStyles} ${isDarkMode ? darkStyles : lightStyles}`;
};

const MediaCard: React.FC<{
    item: MediaItem;
    onClick: () => void;
    isSelectMode: boolean;
    isSelected: boolean;
    onSelect: (id: string) => void;
}> = ({ item, onClick, isSelectMode, isSelected, onSelect }) => {
    const aspectRatio = item.size ? item.size.replace(':', ' / ') : '1 / 1';

    const handleClick = () => {
        if (isSelectMode) {
            onSelect(item.id);
        } else {
            onClick();
        }
    };

    return (
        <div className="relative overflow-hidden rounded-lg group cursor-pointer" onClick={handleClick}>
            <img
                src={item.url}
                alt={item.prompt || item.author}
                className="w-full h-auto object-cover transition-transform duration-300 group-hover:scale-105"
                style={{ aspectRatio }}
                loading="lazy"
            />

            {/* 选择模式的选中状态覆盖层 */}
            {isSelectMode && (
                <div className={`absolute inset-0 transition-all duration-200 ${isSelected
                    ? 'bg-blue-500/30 border-4 border-blue-500'
                    : 'border-2 border-gray-300 hover:border-gray-400'
                    }`}>
                    <div className="absolute top-2 right-2">
                        <div className={`w-6 h-6 rounded-full flex items-center justify-center ${isSelected
                            ? 'bg-blue-500 text-white'
                            : 'bg-white/80 text-gray-600'
                            }`}>
                            {isSelected ? <CheckCircle size={16} /> : <div className="w-3 h-3 border-2 border-gray-400 rounded-full" />}
                        </div>
                    </div>
                </div>
            )}

            {/* 悬停信息覆盖层 */}
            {!isSelectMode && (
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <div className="absolute bottom-0 left-0 p-3 w-full flex items-center justify-between text-white">
                        <div className="flex items-center">
                            <img src={item.authorAvatar} alt={item.author} className="w-6 h-6 rounded-full mr-2" />
                            <span className="text-xs font-medium">{item.author}</span>
                        </div>
                        <div className="flex items-center space-x-3">
                            <button className="text-white hover:text-gray-300"><Search size={14} /></button>
                            <button className="text-white hover:text-red-500"><Heart size={14} /></button>
                            <span className="text-xs">{item.likes / 1000}k</span>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

interface LibraryProps {
    isDarkMode: boolean;
}

const Library: React.FC<LibraryProps> = ({ isDarkMode }) => {
    const [items, setItems] = useState<MediaItem[]>([]);
    const [prompt, setPrompt] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const { token, userInfo } = useAuth();
    const [page, setPage] = useState(0);
    const [isLoadingMore, setIsLoadingMore] = useState(false);
    const [hasMore, setHasMore] = useState(true);
    const mainContentRef = useRef<HTMLElement>(null);
    const observer = useRef<IntersectionObserver>();
    const [selectedItemIndex, setSelectedItemIndex] = useState<number | null>(null);
    const [mediaType, setMediaType] = useState<'image' | 'video'>('image');
    const [aspectRatio, setAspectRatio] = useState('1:1');
    const [generationCount, setGenerationCount] = useState(1);
    const [columnCount, setColumnCount] = useState(4);
    const [masonryKey, setMasonryKey] = useState(Date.now());
    const [activePopup, setActivePopup] = useState<PopupType>(null);
    const popupsContainerRef = useRef<HTMLDivElement>(null);

    // 图片编辑相关状态
    const [uploadedImageUrl, setUploadedImageUrl] = useState<string>('');
    const [isDragging, setIsDragging] = useState(false);
    const fileInputRef = useRef<HTMLInputElement>(null);

    // 批量删除相关状态
    const [isSelectMode, setIsSelectMode] = useState(false);
    const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());
    const [isDeleting, setIsDeleting] = useState(false);
    const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
    const deleteConfirmButtonRef = useRef<HTMLButtonElement>(null);

    // 从本地缓存加载设置
    useEffect(() => {
        const savedSettings = localStorage.getItem('library-settings');
        if (savedSettings) {
            try {
                const settings = JSON.parse(savedSettings);
                if (settings.aspectRatio) setAspectRatio(settings.aspectRatio);
                if (settings.generationCount) setGenerationCount(settings.generationCount);
                if (settings.columnCount) {
                    setColumnCount(settings.columnCount);
                    setMasonryKey(Date.now());
                }
                if (settings.mediaType) setMediaType(settings.mediaType);
            } catch (error) {
                console.error('Failed to load settings from localStorage:', error);
            }
        }
    }, []);

    // 保存设置到本地缓存
    const saveSettingsToLocal = useCallback((newSettings: Partial<LibrarySettings>) => {
        try {
            const currentSettings = JSON.parse(localStorage.getItem('library-settings') || '{}');
            const updatedSettings = { ...currentSettings, ...newSettings };
            localStorage.setItem('library-settings', JSON.stringify(updatedSettings));
        } catch (error) {
            console.error('Failed to save settings to localStorage:', error);
        }
    }, []);

    // 优化的列数切换处理器
    const handleColumnCountChange = useCallback((count: ColumnCount) => {
        setColumnCount(count);
        setMasonryKey(Date.now());
        saveSettingsToLocal({ columnCount: count });
        setActivePopup(null);

        // 强制触发resize事件来让ResponsiveMasonry重新计算
        setTimeout(() => {
            window.dispatchEvent(new Event('resize'));
        }, 50);
    }, [saveSettingsToLocal]);

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            // 检查是否点击在底部输入框的弹窗容器内
            if (popupsContainerRef.current && !popupsContainerRef.current.contains(event.target as Node)) {
                // 只关闭底部输入框相关的弹窗，不关闭布局弹窗
                if (activePopup && activePopup !== 'layout') {
                    setActivePopup(null);
                }
            }

            // 处理布局弹窗的点击外部关闭
            const layoutPopup = document.querySelector('[data-popup="layout"]');
            const layoutButton = document.querySelector('[data-button="layout"]');

            if (activePopup === 'layout' && layoutPopup && layoutButton) {
                const clickedInPopup = layoutPopup.contains(event.target as Node);
                const clickedOnButton = layoutButton.contains(event.target as Node);

                if (!clickedInPopup && !clickedOnButton) {
                    setActivePopup(null);
                }
            }
        };
        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [activePopup]);

    useEffect(() => {
        const handleKeyDown = (event: KeyboardEvent) => {
            if (showDeleteConfirm && !isDeleting) {
                if (event.key === 'Escape') {
                    cancelDelete();
                } else if (event.key === 'Enter') {
                    event.preventDefault();
                    confirmDelete();
                }
            }
        };
        document.addEventListener('keydown', handleKeyDown);
        return () => {
            document.removeEventListener('keydown', handleKeyDown);
        };
    }, [showDeleteConfirm, isDeleting]);

    useEffect(() => {
        if (showDeleteConfirm && deleteConfirmButtonRef.current) {
            deleteConfirmButtonRef.current.focus();
        }
    }, [showDeleteConfirm]);

    const mapAiContentToMediaItem = (content: AiGeneratedContent): MediaItem => ({
        id: content.contentId,
        type: 'image',
        url: content.ossUrl,
        author: userInfo?.actualName || `User ${content.userId}`,
        authorAvatar: userInfo?.avatar || '/ai_avatar.png',
        likes: content.likeCount,
        views: content.viewCount,
        prompt: content.prompt,
        modelUsed: content.modelUsed,
        size: content.size,
    });

    const fetchImages = async (pageNum: number) => {
        if (isLoadingMore || !hasMore || !token) return;

        setIsLoadingMore(true);
        try {
            const response = await getMyGeneratedImages(pageNum, 10, token || undefined);
            if (response.code === 200) {
                const newItems = response.data.content.map(mapAiContentToMediaItem);
                setItems(prev => pageNum === 0 ? newItems : [...prev, ...newItems]);
                setPage(pageNum + 1);
                setHasMore(!response.data.last);
            } else {
                console.error("Failed to fetch images:", response.message);
                setHasMore(false);
            }
        } catch (error) {
            console.error("Failed to fetch images:", error);
            setHasMore(false);
        } finally {
            setIsLoadingMore(false);
        }
    };

    useEffect(() => {
        if (token) {
            setItems([]);
            setPage(0);
            setHasMore(true);
            fetchImages(0);
        }
    }, [token]);

    const lastItemRef = React.useCallback((node: HTMLElement | null) => {
        if (isLoadingMore) return;
        if (observer.current) observer.current.disconnect();
        observer.current = new IntersectionObserver(entries => {
            if (entries[0].isIntersecting && hasMore) {
                fetchImages(page);
            }
        });
        if (node) observer.current.observe(node);
    }, [isLoadingMore, hasMore, page, token]);

    useEffect(() => {
        // This effect is designed to handle the case where the initial content
        // doesn't fill the screen, which would cause the IntersectionObserver
        // to fire prematurely. It programmatically fetches more pages until
        // the content is sufficient to create a scrollbar.
        if (isLoadingMore || !hasMore) {
            return;
        }

        const mainEl = mainContentRef.current;
        // If the container has no scrollbar, and there's more data to fetch, fetch it.
        if (mainEl && mainEl.scrollHeight <= mainEl.clientHeight) {
            fetchImages(page);
        }
    }, [items, isLoadingMore, hasMore, page, token]);

    // 选择相关函数
    const toggleSelectMode = () => {
        setIsSelectMode(!isSelectMode);
        setSelectedItems(new Set());
    };

    const handleSelectItem = (itemId: string) => {
        const newSelected = new Set(selectedItems);
        if (newSelected.has(itemId)) {
            newSelected.delete(itemId);
        } else {
            newSelected.add(itemId);
        }
        setSelectedItems(newSelected);
    };

    const selectAllItems = () => {
        if (selectedItems.size === items.length) {
            setSelectedItems(new Set());
        } else {
            setSelectedItems(new Set(items.map(item => item.id)));
        }
    };

    // 删除函数
    const handleDeleteSelected = () => {
        if (selectedItems.size === 0) return;
        setShowDeleteConfirm(true);
    };

    const confirmDelete = async () => {
        setShowDeleteConfirm(false);
        setIsDeleting(true);

        const itemsToDelete = Array.from(selectedItems);
        const deleteCount = itemsToDelete.length;

        try {
            await deleteGeneratedImages(itemsToDelete, token || undefined);

            // 从本地状态中移除已删除的项目
            setItems(prevItems => prevItems.filter(item => !selectedItems.has(item.id)));
            setSelectedItems(new Set());
            setIsSelectMode(false);

            toast.success(`成功删除 ${deleteCount} 张图片`, {
                duration: 3000,
                style: {
                    background: isDarkMode ? '#374151' : '#fff',
                    color: isDarkMode ? '#fff' : '#374151',
                },
            });
        } catch (error: any) {
            console.error("Failed to delete images:", error);

            let errorMessage = '删除失败，请稍后重试';
            if (error?.message) {
                errorMessage = error.message;
            }

            toast.error(errorMessage, {
                duration: 5000,
                style: {
                    background: isDarkMode ? '#374151' : '#fff',
                    color: isDarkMode ? '#fff' : '#374151',
                },
            });
        } finally {
            setIsDeleting(false);
        }
    };

    const cancelDelete = () => {
        setShowDeleteConfirm(false);
    };

    // 图片上传处理函数
    const handleImageUpload = async (files: File[]) => {
        if (files.length === 0) return;

        // 只处理第一个文件，并且必须是图片
        const file = files[0];
        if (!file.type.startsWith('image/')) {
            toast.error('请选择图片文件', {
                style: {
                    background: isDarkMode ? '#374151' : '#fff',
                    color: isDarkMode ? '#fff' : '#374151',
                },
            });
            return;
        }

        try {
            const response = await uploadFiles({ type: 'personal', files: [file] }, token || undefined);
            if (response.code === 200 && response.data && response.data.length > 0) {
                const uploadedFile = response.data[0];
                setUploadedImageUrl(uploadedFile.path);
                toast.success('图片上传成功，现在可以描述您想要的编辑效果', {
                    style: {
                        background: isDarkMode ? '#374151' : '#fff',
                        color: isDarkMode ? '#fff' : '#374151',
                    },
                });
            } else {
                throw new Error(response.message || 'Upload failed');
            }
        } catch (error: any) {
            console.error("Failed to upload image:", error);
            toast.error(error.message || '图片上传失败', {
                style: {
                    background: isDarkMode ? '#374151' : '#fff',
                    color: isDarkMode ? '#fff' : '#374151',
                },
            });
        }
    };

    const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(false);
        const files = Array.from(e.dataTransfer.files);
        handleImageUpload(files);
    };

    const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(true);
    };

    const handleDragEnter = (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(true);
    };

    const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(false);
    };

    const handleGenerateImage = async () => {
        if (!prompt.trim() || isLoading || mediaType === 'video') return;

        setIsLoading(true);
        try {
            let res;
            if (uploadedImageUrl) {
                // 有上传图片时进行图片编辑
                const editRequest: ImageEditRequest = {
                    prompt: prompt,
                    model: "black-forest-labs/FLUX.1-kontext-max",
                    imageUrl: uploadedImageUrl,
                    n: generationCount,
                    aspectRatio: aspectRatio as '1:1' | '16:9' | '9:16' | '4:3' | '3:4',
                };
                res = await editImage(editRequest, token || undefined);
            } else {
                // 没有上传图片时进行图片生成
                res = await generateImage({
                    prompt: prompt,
                    model: "vertex_ai/imagen-4.0",
                    n: generationCount,
                    aspectRatio: aspectRatio as '1:1' | '16:9' | '9:16' | '4:3' | '3:4',
                }, token || undefined);
            }

            const newItems: MediaItem[] = res.data.map((img, index) => ({
                id: `${res.created}-${index}`,
                type: 'image',
                url: img.url,
                author: userInfo?.actualName || 'Unknown',
                authorAvatar: userInfo?.avatar || '/ai_avatar.png',
                likes: 0,
                views: 0,
                prompt: prompt,
                modelUsed: uploadedImageUrl ? "black-forest-labs/FLUX.1-kontext-max" : "vertex_ai/imagen-4.0",
                size: aspectRatio,
            }));

            setItems(prevItems => [...newItems, ...prevItems]);
            setPrompt('');
            setActivePopup(null);

            // 编辑完成后清除上传的图片
            if (uploadedImageUrl) {
                setUploadedImageUrl('');
            }

        } catch (error: any) {
            console.error(`Failed to ${uploadedImageUrl ? 'edit' : 'generate'} image:`, error);

            // 根据错误类型提供更具体的错误提示
            let errorMessage = uploadedImageUrl ? '图片编辑失败，请稍后重试' : '图片生成失败，请稍后重试';

            if (error?.message) {
                const message = error.message.toLowerCase();
                if (message.includes('invalid_grant') || message.includes('account not found')) {
                    errorMessage = '图片服务认证失败，请联系管理员检查服务配置';
                } else if (message.includes('quota') || message.includes('limit')) {
                    errorMessage = '已达到图片处理配额限制，请稍后重试';
                } else if (message.includes('network') || message.includes('timeout')) {
                    errorMessage = '网络连接异常，请检查网络连接后重试';
                } else if (message.includes('系统异常')) {
                    errorMessage = error.message;
                }
            }

            toast.error(errorMessage, {
                duration: 5000,
                style: {
                    background: isDarkMode ? '#374151' : '#fff',
                    color: isDarkMode ? '#fff' : '#374151',
                },
            });
        } finally {
            setIsLoading(false);
        }
    };

    // 优化的主题样式
    const theme = {
        background: isDarkMode ? 'bg-gray-900' : 'bg-white',
        text: isDarkMode ? 'text-white' : 'text-gray-800',
        inputBackground: isDarkMode ? 'bg-gray-800' : 'bg-gray-100',
        button: isDarkMode ? 'bg-gray-700 hover:bg-gray-600' : 'bg-gray-200 hover:bg-gray-300',
        border: isDarkMode ? 'border-gray-700' : 'border-gray-200',
        headerBg: isDarkMode ? 'rgba(17, 24, 39, 0.8)' : 'rgba(255, 255, 255, 0.8)',
        popup: isDarkMode ? 'bg-neutral-900 border border-gray-700' : 'bg-white border border-gray-200',
        emptyIcon: isDarkMode ? 'text-gray-600' : 'text-gray-400',
        emptyTitle: isDarkMode ? 'text-gray-400' : 'text-gray-700',
        emptyText: isDarkMode ? 'text-gray-500' : 'text-gray-500'
    };

    const innerButtonBgClass = (isDarkMode
        ? 'bg-white/10 hover:bg-white/20 border border-white/10 hover:border-white/20'
        : 'bg-black/5 hover:bg-black/10 border border-black/10 hover:border-black/15') + ' transition-all duration-200 backdrop-blur-sm';

    return (
        <div className={`flex flex-col w-full h-full ${theme.background} ${theme.text} relative`}>
            {/* 工具栏 */}
            <div className={`flex-shrink-0 z-10 flex items-center justify-between p-4 border-b ${theme.border}`}
                style={{ backgroundColor: theme.headerBg }}>
                <h1 className="text-xl font-bold">Library</h1>
                <div className="flex items-center space-x-2">
                    {isSelectMode ? (
                        <>
                            <button
                                onClick={selectAllItems}
                                className={`px-3 py-1.5 text-sm rounded-lg transition-colors ${selectedItems.size === items.length
                                    ? (isDarkMode ? 'bg-blue-600 text-white hover:bg-blue-700' : 'bg-blue-500 text-white hover:bg-blue-600')
                                    : (isDarkMode ? 'bg-gray-700 text-gray-300 hover:bg-gray-600' : 'bg-gray-200 text-gray-700 hover:bg-gray-300')
                                    }`}
                            >
                                {selectedItems.size === items.length ? '取消全选' : '全选'}
                            </button>
                            <button
                                onClick={handleDeleteSelected}
                                disabled={selectedItems.size === 0 || isDeleting}
                                className={`px-3 py-1.5 text-sm rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed
                                    ${isDarkMode
                                        ? 'bg-red-600 text-white hover:bg-red-700 disabled:bg-red-800'
                                        : 'bg-red-500 text-white hover:bg-red-600 disabled:bg-red-400'
                                    }`}
                            >
                                {isDeleting ? (
                                    <>
                                        <Loader2 size={14} className="animate-spin mr-1" />
                                        删除中...
                                    </>
                                ) : (
                                    `删除 (${selectedItems.size})`
                                )}
                            </button>
                            <button
                                onClick={toggleSelectMode}
                                className={`p-2 rounded-lg transition-colors ${isDarkMode
                                    ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700'
                                    : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100'
                                    }`}
                            >
                                <X size={18} />
                            </button>
                        </>
                    ) : (
                        <>

                            <div className="relative">
                                <button
                                    data-button="layout"
                                    onClick={() => setActivePopup(activePopup === 'layout' ? null : 'layout')}
                                    className={`flex items-center space-x-1 px-3 py-1.5 text-sm rounded-lg transition-colors ${isDarkMode
                                        ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700'
                                        : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100'
                                        }`}
                                    title="调整布局"
                                >
                                    <Grid3X3 size={16} />
                                    <span>{columnCount}列</span>
                                </button>
                                {activePopup === 'layout' && (
                                    <div data-popup="layout" className={`absolute top-full right-0 mt-2 w-32 rounded-lg shadow-lg p-1 z-20 ${theme.popup}`}>
                                        {COLUMN_OPTIONS.map(count => (
                                            <button
                                                key={count}
                                                onClick={() => handleColumnCountChange(count)}
                                                className={`w-full text-left px-3 py-2 text-sm rounded-md transition-colors ${columnCount === count
                                                    ? (isDarkMode ? 'bg-blue-600 text-white' : 'bg-blue-500 text-white')
                                                    : (isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100')
                                                    }`}
                                            >
                                                {count}列布局
                                            </button>
                                        ))}
                                    </div>
                                )}
                            </div>
                            <button
                                onClick={toggleSelectMode}
                                className={`p-2 rounded-lg transition-colors ${isDarkMode
                                    ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700'
                                    : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100'
                                    }`}
                                title="批量删除"
                            >
                                <Trash2 size={18} />
                            </button>
                        </>
                    )}
                </div>
            </div>

            {/* Masonry Grid */}
            <main ref={mainContentRef} className="flex-grow p-4 pb-32 overflow-y-auto">
                {items.length > 0 ? (
                    <>
                        <div style={{ width: '100%' }}>
                            <ResponsiveMasonry
                                key={masonryKey}
                                columnsCountBreakPoints={{
                                    [RESPONSIVE_BREAKPOINTS.mobile]: 1,
                                    [RESPONSIVE_BREAKPOINTS.tablet]: Math.min(columnCount, 2),
                                    [RESPONSIVE_BREAKPOINTS.desktop]: columnCount
                                }}
                            >
                                <Masonry gutter="1rem">
                                    {items.map((item, index) => {
                                        const card = (
                                            <MediaCard
                                                item={item}
                                                onClick={() => setSelectedItemIndex(index)}
                                                isSelectMode={isSelectMode}
                                                isSelected={selectedItems.has(item.id)}
                                                onSelect={handleSelectItem}
                                            />
                                        );
                                        if (items.length === index + 1) {
                                            return <div ref={lastItemRef} key={item.id}>{card}</div>
                                        }
                                        return <div key={item.id}>{card}</div>
                                    })}
                                </Masonry>
                            </ResponsiveMasonry>
                            {isLoadingMore && (
                                <div className="flex justify-center items-center p-4">
                                    <Loader2 size={24} className="animate-spin" />
                                    <span className="ml-2">Loading more...</span>
                                </div>
                            )}
                        </div>
                    </>
                ) : (
                    <div className="flex flex-col items-center justify-center h-full text-center">
                        {isLoadingMore ? (
                            <div className="flex justify-center items-center p-4">
                                <Loader2 size={32} className="animate-spin" />
                                <span className="ml-4 text-lg">Loading images...</span>
                            </div>
                        ) : (
                            <div className={`text-center`}>
                                <Inbox size={64} className={`mx-auto mb-4 ${theme.emptyIcon}`} />
                                <h2 className={`text-2xl font-semibold mb-2 ${theme.emptyTitle}`}>您的图库是空的</h2>
                                <p className={theme.emptyText}>
                                    请在底部的浮动输入框中描述您想要的图片。
                                </p>
                            </div>
                        )}
                    </div>
                )}
            </main>

            {/* 浮动输入框 - 仅在右侧内容区域 */}
            <div className="absolute bottom-4 left-4 right-4 z-50 pointer-events-none">
                <div className="w-full max-w-3xl mx-auto relative z-10 pointer-events-auto">
                    <div
                        className={`flex items-start p-4 rounded-2xl shadow-2xl backdrop-blur-xl border transform transition-all duration-300 hover:scale-[1.02] hover:shadow-3xl ${isDarkMode
                            ? 'bg-gray-900/90 border-gray-700/70 text-white shadow-black/50 hover:bg-gray-900/95'
                            : 'bg-white/90 border-white/50 text-black shadow-gray-900/30 hover:bg-white/95'
                            } ${isDeleting ? 'opacity-50 pointer-events-none' : ''} ${isDragging ? (isDarkMode ? 'border-blue-500 bg-blue-900/20' : 'border-blue-400 bg-blue-50/50') : ''}`}
                        onDrop={handleDrop}
                        onDragOver={handleDragOver}
                        onDragEnter={handleDragEnter}
                        onDragLeave={handleDragLeave}
                    >
                        <div className="flex-grow flex flex-col space-y-3">
                            {/* 上传的图片显示 */}
                            {uploadedImageUrl && (
                                <div className="relative">
                                    <img
                                        src={uploadedImageUrl}
                                        alt="上传的图片"
                                        className="w-20 h-20 object-cover rounded-lg border-2 border-gray-300"
                                    />
                                    <button
                                        onClick={() => setUploadedImageUrl('')}
                                        className={`absolute -top-2 -right-2 p-1 rounded-full ${isDarkMode ? 'bg-gray-800 text-white hover:bg-gray-700' : 'bg-white text-gray-600 hover:bg-gray-100'} shadow-lg transition-colors`}
                                        title="移除图片"
                                    >
                                        <X size={12} />
                                    </button>
                                </div>
                            )}

                            {/* 隐藏的文件输入框 */}
                            <input
                                ref={fileInputRef}
                                type="file"
                                accept="image/*"
                                style={{ display: 'none' }}
                                onChange={(e) => {
                                    if (e.target.files) {
                                        handleImageUpload(Array.from(e.target.files));
                                    }
                                }}
                            />

                            {/* Input row */}
                            <div className="flex items-start w-full">
                                <TextareaAutosize
                                    value={prompt}
                                    onChange={(e) => setPrompt(e.target.value)}
                                    placeholder={uploadedImageUrl ? "描述您想要对图片进行的编辑..." : "Describe your image..."}
                                    className={`w-full bg-transparent px-3 py-2.5 text-base focus:outline-none resize-none ${isDarkMode
                                        ? 'placeholder-gray-400 text-white'
                                        : 'placeholder-gray-500 text-gray-900'
                                        }`}
                                    maxRows={5}
                                    onKeyDown={(e) => {
                                        if (e.key === 'Enter' && !e.shiftKey) {
                                            e.preventDefault();
                                            handleGenerateImage();
                                        }
                                    }}
                                />
                            </div>
                            {/* Buttons row */}
                            <div ref={popupsContainerRef} className="flex items-center space-x-2 px-2 pb-1">
                                <div className="relative">
                                    <button
                                        onClick={() => setActivePopup(activePopup === 'media' ? null : 'media')}
                                        className={`flex items-center space-x-1.5 px-3 py-1.5 text-sm rounded-lg ${innerButtonBgClass}`}>
                                        <ImageIcon size={16} />
                                        <span>{mediaType === 'image' ? 'Image' : 'Video'}</span>
                                    </button>
                                    {activePopup === 'media' && (
                                        <div className={`absolute bottom-full left-0 mb-2 w-32 rounded-lg shadow-xl p-1 z-20 backdrop-blur-xl border ${isDarkMode
                                            ? 'bg-gray-900/80 border-gray-700/50'
                                            : 'bg-white/80 border-white/20'
                                            }`}>
                                            <button onClick={() => {
                                                setMediaType('image');
                                                saveSettingsToLocal({ mediaType: 'image' });
                                                setActivePopup(null);
                                            }} className={`w-full text-left px-3 py-2 text-sm rounded-md ${isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'}`}>Image</button>
                                            <button
                                                disabled
                                                className={`w-full text-left px-3 py-2 text-sm rounded-md opacity-50 cursor-not-allowed ${isDarkMode ? 'text-gray-500' : 'text-gray-400'}`}
                                            >
                                                Video (即将推出)
                                            </button>
                                        </div>
                                    )}
                                </div>
                                <div className="relative">
                                    <button
                                        onClick={() => setActivePopup(activePopup === 'ratio' ? null : 'ratio')}
                                        className={`flex items-center space-x-1.5 px-3 py-1.5 text-sm rounded-lg ${innerButtonBgClass}`}>
                                        <Smartphone size={16} />
                                        <span>{aspectRatio}</span>
                                    </button>
                                    {activePopup === 'ratio' && (
                                        <div className={`absolute bottom-full left-0 mb-2 w-48 rounded-lg shadow-xl p-1 z-20 backdrop-blur-xl border ${isDarkMode
                                            ? 'bg-gray-900/80 border-gray-700/50'
                                            : 'bg-white/80 border-white/20'
                                            }`}>
                                            {ASPECT_RATIO_OPTIONS.map(({ ratio, resolution }) => (
                                                <button
                                                    key={ratio}
                                                    onClick={() => {
                                                        setAspectRatio(ratio);
                                                        saveSettingsToLocal({ aspectRatio: ratio });
                                                        setActivePopup(null);
                                                    }}
                                                    className={`w-full flex items-center justify-between px-3 py-2 text-sm rounded-md ${isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'}`}
                                                >
                                                    <span>{ratio}</span>
                                                    <span className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>{resolution}</span>
                                                </button>
                                            ))}
                                        </div>
                                    )}
                                </div>
                                <div className="relative">
                                    <button
                                        onClick={() => setActivePopup(activePopup === 'count' ? null : 'count')}
                                        className={`flex items-center space-x-1.5 px-3 py-1.5 text-sm rounded-lg ${innerButtonBgClass}`}>
                                        <Layers size={16} />
                                        <span>{generationCount}v</span>
                                    </button>
                                    {activePopup === 'count' && (
                                        <div className={`absolute bottom-full left-0 mb-2 w-32 rounded-lg shadow-xl p-1 z-20 backdrop-blur-xl border ${isDarkMode
                                            ? 'bg-gray-900/80 border-gray-700/50'
                                            : 'bg-white/80 border-white/20'
                                            }`}>
                                            {GENERATION_COUNT_OPTIONS.map(count => (
                                                <button key={count} onClick={() => {
                                                    setGenerationCount(count);
                                                    saveSettingsToLocal({ generationCount: count });
                                                    setActivePopup(null);
                                                }} className={`w-full text-left px-3 py-2 text-sm rounded-md ${isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'}`}>{count}</button>
                                            ))}
                                        </div>
                                    )}
                                </div>
                                <button
                                    onClick={() => fileInputRef.current?.click()}
                                    className={`p-2 rounded-lg ${innerButtonBgClass}`}
                                    title="上传图片进行编辑"
                                >
                                    <ImageIcon size={16} />
                                </button>
                                <button className={`p-2 rounded-lg ${innerButtonBgClass}`}>
                                    <Settings2 size={16} />
                                </button>
                                <div className="relative group">
                                    <button className={`p-2 rounded-lg ${innerButtonBgClass}`}>
                                        <HelpCircle size={16} />
                                    </button>
                                    <div className={`absolute bottom-full right-0 mb-2 w-80 rounded-lg shadow-2xl p-4 z-20 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 backdrop-blur-xl border ${isDarkMode
                                        ? 'bg-gray-900/90 border-gray-700/50'
                                        : 'bg-white/90 border-white/30'
                                        }`}>
                                        <div className="flex flex-col space-y-3">
                                            <div>
                                                <h4 className={`text-sm font-semibold mb-2 ${isDarkMode ? 'text-gray-200' : 'text-gray-800'}`}>
                                                    🎨 如何使用
                                                </h4>
                                                <p className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                                                    <strong>图片生成：</strong>在输入框中详细描述您想要的图片。<br />
                                                    <strong>图片编辑：</strong>点击图片按钮或拖拽图片到输入框，然后描述编辑效果。
                                                </p>
                                            </div>
                                            <div>
                                                <h4 className={`text-sm font-semibold mb-2 ${isDarkMode ? 'text-blue-400' : 'text-blue-600'}`}>
                                                    ✨ 图片编辑功能
                                                </h4>
                                                <p className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                                                    支持对上传的图片进行AI编辑，如风格转换、物体添加/移除、背景替换等。
                                                </p>
                                            </div>
                                            <div>
                                                <h4 className={`text-sm font-semibold mb-2 ${isDarkMode ? 'text-amber-400' : 'text-amber-600'}`}>
                                                    ⚠️ 重要提示
                                                </h4>
                                                <p className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                                                    涉及政治敏感、暴力色情等内容的图片可能生成失败，请确保内容符合规范。
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <button
                            onClick={handleGenerateImage}
                            disabled={isLoading || !prompt.trim() || mediaType === 'video'}
                            className={`ml-2 mt-1 p-2 rounded-full ${innerButtonBgClass} self-start disabled:opacity-50 disabled:cursor-not-allowed`}
                        >
                            {isLoading ? <Loader2 size={20} className="animate-spin" /> : <ArrowUp size={20} />}
                        </button>
                    </div>
                </div>
            </div>

            {selectedItemIndex !== null && (
                <ImageViewerModal
                    items={items}
                    currentIndex={selectedItemIndex}
                    isDarkMode={isDarkMode}
                    onClose={() => setSelectedItemIndex(null)}
                    onNavigate={(newIndex) => setSelectedItemIndex(newIndex)}
                />
            )}

            {/* 删除确认弹窗 */}
            {showDeleteConfirm && (
                <div
                    className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50 animate-in fade-in duration-200"
                    onClick={(e) => {
                        if (e.target === e.currentTarget && !isDeleting) {
                            cancelDelete();
                        }
                    }}
                >
                    <div className={`max-w-md w-full mx-4 rounded-lg shadow-2xl border transform transition-all duration-200 animate-in zoom-in-95 ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
                        } ${isDeleting ? 'animate-pulse' : ''}`}>
                        <div className="p-6">
                            <div className="flex items-center justify-between mb-4">
                                <div className="flex items-center">
                                    <div className={`w-10 h-10 rounded-full flex items-center justify-center mr-3 ${isDarkMode ? 'bg-red-900/20' : 'bg-red-100'
                                        }`}>
                                        <Trash2 className="w-5 h-5 text-red-500" />
                                    </div>
                                    <h3 className={`text-lg font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'
                                        }`}>
                                        {isDeleting ? '删除中...' : '确认删除'}
                                    </h3>
                                </div>
                                {!isDeleting && (
                                    <button
                                        onClick={cancelDelete}
                                        className={`p-1 rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 ${isDarkMode
                                            ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700 focus:ring-gray-500 focus:ring-offset-gray-800'
                                            : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100 focus:ring-gray-500 focus:ring-offset-white'
                                            }`}
                                    >
                                        <X className="w-5 h-5" />
                                    </button>
                                )}
                            </div>
                            <div className={`text-sm mb-6 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'
                                }`}>
                                {isDeleting ? (
                                    <div className="flex items-center justify-center p-4">
                                        <Loader2 className="w-5 h-5 animate-spin mr-3 text-red-500" />
                                        <span className="text-base">正在删除 {selectedItems.size} 张图片...</span>
                                    </div>
                                ) : (
                                    <>
                                        <p className="mb-2">
                                            您确定要删除选中的 <span className="font-semibold text-red-500">{selectedItems.size}</span> 张图片吗？
                                        </p>
                                        <div className="flex items-center mt-3 p-3 rounded-lg bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800">
                                            <div className="flex-shrink-0 mr-2">
                                                <svg className="w-4 h-4 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                                                </svg>
                                            </div>
                                            <p className="text-sm text-red-700 dark:text-red-400">
                                                <strong>警告：</strong>此操作不可撤销，删除后无法恢复。
                                            </p>
                                        </div>
                                    </>
                                )}
                            </div>
                            {!isDeleting && (
                                <div className="flex justify-end space-x-3">
                                    <button
                                        onClick={cancelDelete}
                                        className={`px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 ${isDarkMode
                                            ? 'bg-gray-700 text-gray-300 hover:bg-gray-600 focus:ring-gray-500 focus:ring-offset-gray-800'
                                            : 'bg-gray-200 text-gray-700 hover:bg-gray-300 focus:ring-gray-500 focus:ring-offset-white'
                                            }`}
                                    >
                                        取消
                                        <span className={`ml-1 text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                                            (Esc)
                                        </span>
                                    </button>
                                    <button
                                        ref={deleteConfirmButtonRef}
                                        onClick={confirmDelete}
                                        className={`px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 transform hover:scale-105 active:scale-95 focus:outline-none focus:ring-2 focus:ring-offset-2 ${isDarkMode
                                            ? 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 focus:ring-offset-gray-800'
                                            : 'bg-red-500 text-white hover:bg-red-600 focus:ring-red-500 focus:ring-offset-white'
                                            }`}
                                    >
                                        确认删除
                                        <span className={`ml-1 text-xs ${isDarkMode ? 'text-red-200' : 'text-red-100'}`}>
                                            (Enter)
                                        </span>
                                    </button>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default Library; 