import React, { useState, useRef, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
    ChatInput,
    ChatHistory,
    AgentApps,
    WelcomeScreen,
    SessionSettingsDrawer,
    AttachmentViewer,
    AttachmentContent
} from '../components/chat';
import { useStreamChatCompletion } from '../hooks/useOpenAI';
import { useSession, useAuth } from '../hooks';
import { createUserMessage, createSystemMessage } from '../utils/openaiHelpers';
import { chatSessionApi, appsApi } from '../utils/api';
import { Message } from '../components/chat/ChatHistory';
import { SessionData } from '../utils/api';
import { UploadedFileDetail } from '../types';

interface ChatInterfaceProps {
    sidebarVisible: boolean;
    isDarkMode: boolean;
    onToggleSidebar: () => void;
    onToggleTheme: () => void;
}

const ChatInterface: React.FC<ChatInterfaceProps> = ({
    sidebarVisible,
    isDarkMode,
    onToggleSidebar,
    onToggleTheme
}) => {
    const { sessionId } = useParams<{ sessionId: string }>();
    const { token, userInfo, isAuthenticated } = useAuth();
    const navigate = useNavigate();
    const [messages, setMessages] = useState<Message[]>([]);
    const [inputValue, setInputValue] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [isHistoryLoading, setIsHistoryLoading] = useState(false);
    const [isTransitioning, setIsTransitioning] = useState(false);
    const [streamingMessageId, setStreamingMessageId] = useState<string | null>(null);
    const [sessionMetadata, setSessionMetadata] = useState<any>(null);
    const [isCreatingAgentChat, setIsCreatingAgentChat] = useState(false);
    const [isSettingsDrawerOpen, setIsSettingsDrawerOpen] = useState(false);
    const [isAttachmentViewerOpen, setIsAttachmentViewerOpen] = useState(false);
    const [selectedAttachment, setSelectedAttachment] = useState<AttachmentContent | null>(null);

    // 使用流式 OpenAI hook
    const streamChatMutation = useStreamChatCompletion();

    // 使用会话管理 hook
    const { currentSessionId, createNewSession, setCurrentSessionId, isLoading: isCreatingSession } = useSession();

    // 用于存储当前流式消息的引用
    const currentStreamingMessage = useRef<Message | null>(null);

    // 判断是否显示欢迎屏幕：没有URL中的sessionId且没有消息记录
    const shouldShowWelcome = !sessionId && messages.length === 0 && !isTransitioning && !isHistoryLoading;

    useEffect(() => {
        const loadSession = async (id: string) => {
            console.log('开始加载会话，会话ID:', id);
            setIsHistoryLoading(true);
            setMessages([]); // 清空旧消息
            setSessionMetadata(null); // 清空元数据

            try {
                // 检查认证状态
                if (!token) {
                    console.error('用户未认证，无法加载会话');
                    return;
                }

                // 并行加载所有会话和当前会话的消息历史
                const [sessionsResponse, messagesResponse] = await Promise.all([
                    chatSessionApi.getSessions(token),
                    chatSessionApi.getSessionMessages(id, token)
                ]);

                // 从所有会话中找到当前会话
                const currentSession = sessionsResponse.data.find(s => s.id === id);

                if (currentSession) {
                    try {
                        const metadata = JSON.parse(currentSession.metadata || '{}');
                        setSessionMetadata(metadata);
                        console.log('会话元数据加载成功:', metadata);
                    } catch (e) {
                        console.error('解析会话元数据失败:', e);
                        setSessionMetadata({
                            chatMode: 'chat',
                            settings: {
                                temperature: 0.2,
                                maxTokens: 128000,
                                knowledgeEnabled: false,
                                webSearchEnabled: false
                            }
                        });
                    }
                } else {
                    console.error(`在会话列表中未找到ID为 ${id} 的会话`);
                }

                // 处理消息历史
                const historyMessages: Message[] = messagesResponse.data
                    .filter(msg => msg.sender !== 'system')
                    .map(msg => {
                        let parsedContent: any = msg.content;
                        let parsedAttachments: UploadedFileDetail[] | undefined = undefined;

                        // 对于用户消息，尝试解析content, 因为它可能是包含多模态内容的JSON字符串
                        if (msg.sender === 'user') {
                            try {
                                const parsed = JSON.parse(msg.content);
                                if (Array.isArray(parsed)) { // 兼容旧的格式
                                    parsedContent = parsed;
                                } else if (parsed && (parsed.parts || parsed.attachments)) { // 新的结构
                                    parsedContent = parsed.parts || [];
                                    parsedAttachments = parsed.attachments;
                                }
                            } catch (e) {
                                // 内容不是JSON格式，保持为字符串
                            }
                        }
                        return {
                            id: msg.id.toString(),
                            type: msg.sender as 'user' | 'assistant',
                            content: parsedContent,
                            timestamp: new Date(msg.createdAt),
                            attachments: parsedAttachments,
                        };
                    });
                setMessages(historyMessages);
                console.log('会话历史加载成功:', historyMessages);

            } catch (error) {
                console.error('加载会话数据失败:', error);
            } finally {
                setIsHistoryLoading(false);
            }
        };

        if (sessionId) {
            setCurrentSessionId(sessionId);
            loadSession(sessionId);
        } else {
            setCurrentSessionId(null);
            setMessages([]); // 如果没有sessionId，确保消息列表是空的
            setSessionMetadata(null); // 同时清空元数据
        }
    }, [sessionId, token, setCurrentSessionId]);

    const handleToggleAttachmentViewer = () => {
        if (!isAttachmentViewerOpen && sidebarVisible) {
            onToggleSidebar();
        }
        setIsAttachmentViewerOpen(prev => !prev);
    };

    const handleCloseAttachmentViewer = () => {
        setIsAttachmentViewerOpen(false);
    };

    const handleAttachmentClick = (attachment: UploadedFileDetail) => {
        const fileExtension = attachment.originalFileName.split('.').pop()?.toLowerCase() || '';

        const getViewerType = (ext: string) => {
            if (['pdf'].includes(ext)) return 'pdf';
            if (['doc', 'docx'].includes(ext)) return 'word';
            if (['xls', 'xlsx'].includes(ext)) return 'excel';
            if (['png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp', 'svg'].includes(ext)) return 'image';
            if (['txt', 'js', 'ts', 'tsx', 'jsx', 'json', 'py', 'go', 'java', 'c', 'cpp', 'cs', 'html', 'css', 'scss', 'less', 'md', 'yaml', 'sh'].includes(ext)) return 'code';
            return 'unsupported';
        };

        setSelectedAttachment({
            type: getViewerType(fileExtension),
            title: attachment.originalFileName,
            url: attachment.path,
        });

        if (!isAttachmentViewerOpen) {
            if (sidebarVisible) {
                onToggleSidebar();
            }
            setIsAttachmentViewerOpen(true);
        }
    };

    const handleSaveSettings = async (newMetadata: any) => {
        if (!sessionId) return;

        // 检查认证状态
        if (!isAuthenticated || !token) {
            console.error('用户未认证，无法更新会话设置');
            return;
        }

        try {
            await chatSessionApi.updateSession(sessionId, {
                metadata: JSON.stringify(newMetadata),
                // a good practice to also update the model used at the session level
                modelUsed: newMetadata.settings?.model,
            }, token);

            setSessionMetadata(newMetadata);
            // Optionally, show a success notification
            console.log('会话设置已更新');

        } catch (error) {
            console.error('更新会话设置失败:', error);
            // Optionally, show an error notification
        }
    };

    const handleSuggestionClick = (suggestion: string) => {
        setInputValue(suggestion);
    };

    const handleModeChange = (mode: 'chat' | 'agent') => {
        setSessionMetadata((prev: any) => ({
            ...prev,
            chatMode: mode,
        }));
    };

    const handleAgentSelect = async (agentId: number, agentName: string) => {
        if (isCreatingAgentChat) return;

        // 检查认证状态
        if (!isAuthenticated || !token) {
            console.error('用户未认证，无法选择Agent');
            return;
        }

        setIsCreatingAgentChat(true);
        try {
            const response = await appsApi.getAppById(agentId, token);
            if (response.code === 200) {
                const appDetail = response.data;
                const metadata = {
                    app: { id: appDetail.id, name: appDetail.name },
                    settings: appDetail.settings,
                };
                const newSession = await createNewSession({
                    sessionTitle: `新建会话`,
                    modelUsed: appDetail.settings.model,
                    metadata: JSON.stringify(metadata),
                });
                if (newSession?.sessionId) {
                    navigate(`/chat/${newSession.sessionId}`);
                } else {
                    console.error('创建会话失败');
                    // 在这里可以添加一些用户提示
                }
            } else {
                console.error(`获取应用 ${agentName} 详情失败:`, response.message);
                // 在这里可以添加一些用户提示
            }
        } catch (err: any) {
            console.error('处理Agent点击时出错:', err);
            // 如果是认证错误，可以提示用户重新登录
            if (err.message && (err.message.includes('登录') || err.message.includes('认证') || err.message.includes('Token已过期'))) {
                console.error('认证失败，请重新登录');
                // 这里可以添加一些用户提示或重定向到登录页面
            }
        } finally {
            setIsCreatingAgentChat(false);
        }
    };

    const handleSendMessage = async (content: any[], text: string, attachments: UploadedFileDetail[] = []) => {
        if (isLoading || isCreatingSession) return;

        console.log('开始发送消息:', content, '附件:', attachments);

        let effectiveSessionId = sessionId || currentSessionId;

        // 如果URL中没有sessionId且还没有会话ID，先创建会话
        if (!sessionId && !currentSessionId && messages.length === 0) {
            try {
                console.log('创建新会话...');
                const defaultModel = 'deepseek-ai/DeepSeek-V3';
                const newMetadata = sessionMetadata || {
                    chatMode: 'chat',
                    settings: {
                        temperature: 0.2,
                        maxTokens: 128000,
                        model: defaultModel,
                        knowledgeEnabled: false,
                        webSearchEnabled: false
                    }
                };

                // 获取要使用的模型：优先使用会话配置的模型，否则使用默认模型
                const modelToUse = newMetadata.settings?.model || defaultModel;

                const sessionResponse = await createNewSession({
                    sessionTitle: '新建会话', // 暂时使用默认标题
                    modelUsed: modelToUse,
                    metadata: JSON.stringify(newMetadata)
                });
                console.log('会话创建成功:', sessionResponse.sessionId);

                // 更新有效的会话ID，但暂时不跳转路由
                effectiveSessionId = sessionResponse.sessionId;
                setSessionMetadata(newMetadata);
            } catch (error) {
                console.error('创建会话失败:', error);
                // 即使创建会话失败，也继续发送消息
            }
        }

        const userMessage: Message = {
            id: Date.now().toString(),
            type: 'user',
            content: content,
            timestamp: new Date(),
            attachments: attachments,
        };

        // 如果是第一条消息，启动过渡动画
        if (messages.length === 0) {
            setIsTransitioning(true);
        }

        setMessages(prev => [...prev, userMessage]);
        setInputValue('');

        // 如果有非图片附件，自动打开第一个附件
        if (attachments.length > 0) {
            const nonImageAttachment = attachments.find(att => {
                const fileExtension = att.originalFileName.split('.').pop()?.toLowerCase() || '';
                return !['png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp', 'svg'].includes(fileExtension);
            });

            if (nonImageAttachment) {
                handleAttachmentClick(nonImageAttachment);
            }
        }

        // 准备发送到API的消息历史
        // 确保使用最新的消息列表来构建历史
        const updatedMessages = [...messages, userMessage];
        const history = updatedMessages.map(m => {
            if (m.type === 'assistant') {
                return { role: 'assistant', content: m.content };
            }
            if (m.type === 'user') {
                // 对于用户消息，确保发送的是包含附件的结构化内容
                const userApiContent = {
                    parts: m.content,
                    attachments: m.attachments || [],
                };
                return createUserMessage(JSON.stringify(userApiContent));
            }
            return null;
        }).filter(Boolean) as { role: 'user' | 'assistant'; content: string }[];

        setIsLoading(true);
        const assistantMessageId = (Date.now() + 1).toString();
        setStreamingMessageId(assistantMessageId);

        const assistantMessage: Message = {
            id: assistantMessageId,
            type: 'assistant',
            content: '',
            timestamp: new Date(),
        };

        setMessages(prev => [...prev, assistantMessage]);

        // 获取会话配置的模型，如果没有配置则使用默认的 deepseek-chat
        const modelToUse = sessionMetadata?.settings?.model || 'deepseek-ai/DeepSeek-V3';
        const temperatureToUse = sessionMetadata?.settings?.temperature || 0.2;
        const maxTokensToUse = sessionMetadata?.settings?.maxTokens || 128000;

        // 从会话metadata中读取开关状态
        const knowledgeEnabled = sessionMetadata?.settings?.knowledgeEnabled || false;
        const webSearchEnabled = sessionMetadata?.settings?.webSearchEnabled || false;

        streamChatMutation.mutate({
            model: modelToUse,
            messages: history,
            temperature: temperatureToUse,
            max_tokens: maxTokensToUse,
            sessionId: effectiveSessionId || undefined,
            knowledgeEnabled: knowledgeEnabled,
            webSearchEnabled: webSearchEnabled,
            onStream: (chunk) => {
                setMessages(prev => prev.map(msg =>
                    msg.id === assistantMessageId
                        ? { ...msg, content: (msg.content || '') + chunk }
                        : msg
                ));
            },
            onComplete: (fullContent) => {
                setIsLoading(false);
                setStreamingMessageId(null);

                // 当消息数量超过5条时，智能生成会话标题
                const totalMessages = messages.length + 2; // +2 包括当前的用户消息和AI回复
                if (totalMessages > 3 && (effectiveSessionId || sessionId)) {
                    const finalMessages = [...messages, userMessage, { ...assistantMessage, content: fullContent }];
                    generateSessionTitle(effectiveSessionId || sessionId!, finalMessages);
                }

                // navigate to the new session URL if it's the first message
                if (!sessionId && effectiveSessionId) {
                    navigate(`/chat/${effectiveSessionId}`);
                }
            },
            onError: (error) => {
                console.error("Streaming Error:", error);
                setMessages(prev => prev.map(msg =>
                    msg.id === assistantMessageId
                        ? { ...msg, content: `抱歉，处理您的请求时发生错误: ${error.message}` }
                        : msg
                ));
                setIsLoading(false);
                setStreamingMessageId(null);
            }
        });
    };

    // 简单的会话标题生成逻辑
    const generateSessionTitle = async (sessionId: string, allMessages: Message[]) => {
        if (!sessionId || !isAuthenticated || !token) return;

        try {
            // 检查当前会话是否需要生成标题（只有默认标题或空标题时才生成）
            const sessionsResponse = await chatSessionApi.getSessions(token);
            const currentSession = sessionsResponse.data.find(s => s.id === sessionId);

            if (currentSession && currentSession.sessionTitle &&
                !['新建会话', '新建对话', ''].includes(currentSession.sessionTitle.trim())) {
                // 已有有意义的标题，不需要重新生成
                return;
            }

            // 获取用户的前几条有意义的消息
            const userMessages = allMessages
                .filter(msg => msg.type === 'user')
                .map(msg => {
                    if (typeof msg.content === 'string') {
                        return msg.content;
                    } else if (Array.isArray(msg.content)) {
                        return msg.content.map(part => part.text || '').join(' ');
                    }
                    return '';
                })
                .filter(content => content.trim().length > 0);

            if (userMessages.length === 0) return;

            // 过滤掉常见的无意义开头
            const meaningfulMessages = userMessages.filter(msg => {
                const trimmed = msg.trim().toLowerCase();
                const greetings = ['你好', 'hi', 'hello', '您好', '在吗', '在不在', '嗨'];
                return !greetings.includes(trimmed) && trimmed.length > 2;
            });

            // 如果有意义的消息，使用它们；否则使用原始消息
            const messagesToUse = meaningfulMessages.length > 0 ? meaningfulMessages : userMessages;

            // 合并前几条消息生成标题
            const combinedText = messagesToUse.slice(0, 3).join(' ');

            // 生成标题
            const title = combinedText.length > 20 ? combinedText.substring(0, 20) + '...' : combinedText;

            // 更新会话标题
            await chatSessionApi.updateSession(sessionId, {
                sessionTitle: title
            }, token);

            console.log('会话标题已更新:', title);
        } catch (error) {
            console.error('生成会话标题失败:', error);
        }
    };

    const handleCopy = (text: string) => {
        navigator.clipboard.writeText(text);
        // 你可以在这里添加一个 toast 通知
    };

    const handleDelete = (messageId: string) => {
        setMessages(prev => prev.filter(m => m.id !== messageId));
        // 你可能还需要调用API来从数据库中删除
    };

    const handleRetry = (messageId: string) => {
        const messageToRetry = messages.find(m => m.id === messageId);
        if (messageToRetry && typeof messageToRetry.content === 'string') {
            setMessages(prev => prev.filter(m => m.id !== messageId && m.id !== (parseInt(messageId) + 1).toString()));
            setInputValue(messageToRetry.content);
            // 这里可以做一个更优雅的实现，比如直接重新发送
            // handleSendMessage();
        } else if (messageToRetry && Array.isArray(messageToRetry.content)) {
            //  TODO: Handle retry for multi-modal content
            console.log("Retry for multi-modal messages is not implemented yet.");
        }
    };

    const handleRegenerate = async (messageId: string) => {
        if (isLoading || isCreatingSession) return;

        // 找到要重新生成的消息
        const messageIndex = messages.findIndex(m => m.id === messageId);
        if (messageIndex === -1) return;

        // 找到在它之前的用户消息
        const userMessageIndex = messageIndex - 1;
        if (userMessageIndex < 0 || messages[userMessageIndex].type !== 'user') {
            console.error('未找到对应的用户消息');
            return;
        }

        const userMessage = messages[userMessageIndex];

        // 获取重新生成之前的消息历史（不包括当前助手消息）
        const previousMessages = messages.slice(0, messageIndex);

        // 删除当前助手消息
        setMessages(previousMessages);

        // 准备发送到API的消息历史
        const history = previousMessages.map(m => {
            if (m.type === 'assistant') {
                return { role: 'assistant', content: m.content };
            }
            if (m.type === 'user') {
                // 对于用户消息，确保发送的是包含附件的结构化内容
                const userApiContent = {
                    parts: m.content,
                    attachments: m.attachments || [],
                };
                return createUserMessage(JSON.stringify(userApiContent));
            }
            return null;
        }).filter(Boolean) as { role: 'user' | 'assistant'; content: string }[];

        setIsLoading(true);
        const assistantMessageId = (Date.now() + 1).toString();
        setStreamingMessageId(assistantMessageId);

        const assistantMessage: Message = {
            id: assistantMessageId,
            type: 'assistant',
            content: '',
            timestamp: new Date(),
        };

        setMessages(prev => [...prev, assistantMessage]);

        // 获取会话配置的模型，如果没有配置则使用默认的 deepseek-chat
        const modelToUse = sessionMetadata?.settings?.model || 'deepseek-ai/DeepSeek-V3';
        const temperatureToUse = sessionMetadata?.settings?.temperature || 0.2;
        const maxTokensToUse = sessionMetadata?.settings?.maxTokens || 128000;

        const effectiveSessionId = sessionId || currentSessionId;

        // 从会话metadata中读取开关状态
        const knowledgeEnabled = sessionMetadata?.settings?.knowledgeEnabled || false;
        const webSearchEnabled = sessionMetadata?.settings?.webSearchEnabled || false;

        streamChatMutation.mutate({
            model: modelToUse,
            messages: history,
            temperature: temperatureToUse,
            max_tokens: maxTokensToUse,
            sessionId: effectiveSessionId || undefined,
            knowledgeEnabled: knowledgeEnabled,
            webSearchEnabled: webSearchEnabled,
            onStream: (chunk) => {
                setMessages(prev => prev.map(msg =>
                    msg.id === assistantMessageId
                        ? { ...msg, content: (msg.content || '') + chunk }
                        : msg
                ));
            },
            onComplete: (fullContent) => {
                setIsLoading(false);
                setStreamingMessageId(null);
            },
            onError: (error) => {
                console.error("重新生成失败:", error);
                setMessages(prev => prev.map(msg =>
                    msg.id === assistantMessageId
                        ? { ...msg, content: `抱歉，重新生成时发生错误: ${error.message}` }
                        : msg
                ));
                setIsLoading(false);
                setStreamingMessageId(null);
            }
        });
    };

    const themeClasses = {
        background: isDarkMode ? 'bg-gray-900' : 'bg-white',
        text: isDarkMode ? 'text-white' : 'text-gray-900',
    };

    return (
        <div className={`flex flex-col h-full overflow-hidden transition-colors duration-300`}>
            <main className="flex-1 min-h-0">
                {shouldShowWelcome ? (
                    // 欢迎页面 - 整体垂直居中
                    <div className="flex-1 flex flex-col justify-center items-center px-8 h-full">
                        <div className="max-w-4xl w-full">
                            {/* 欢迎文字部分 */}
                            <div className="mb-8">
                                <h1 className={`text-3xl md:text-4xl font-light ${themeClasses.text} mb-4 leading-tight text-left animate-slideInUp animation-delay-200`}>
                                    您好，我是 <span className="text-purple-500 font-medium">锦湖日丽 AI</span>
                                </h1>
                                <p className={`text-lg md:text-xl ${isDarkMode ? 'text-gray-400' : 'text-gray-600'} mb-2 text-left animate-slideInUp animation-delay-300`}>
                                    我可以帮您：编写代码、处理文档、回答问题、创意写作、数据分析等
                                </p>
                            </div>

                            {/* 输入框部分 - 与欢迎文字保持相同宽度 */}
                            <div className="animate-slideInUp animation-delay-400">
                                <ChatInput
                                    value={inputValue}
                                    onChange={setInputValue}
                                    onSend={handleSendMessage}
                                    isLoading={isLoading || isCreatingAgentChat}
                                    isDarkMode={isDarkMode}
                                    placeholder="您好，请和我对话..."
                                    isWelcomeMode={true}
                                    onModeChange={handleModeChange}
                                    onToggleSettings={() => setIsSettingsDrawerOpen(true)}
                                    sessionId={sessionId}
                                    sessionMetadata={sessionMetadata}
                                    onUpdateSessionMetadata={handleSaveSettings}
                                />
                            </div>

                            {/* Agent应用列表 */}
                            <div className="mt-8 animate-slideInUp animation-delay-500">
                                <AgentApps
                                    isDarkMode={isDarkMode}
                                    onAgentSelect={handleAgentSelect}
                                    isWelcomeMode={true}
                                />
                            </div>
                        </div>
                    </div>
                ) : (
                    // 对话模式 - 使用固定高度布局
                    <div className="flex h-full">
                        <div
                            className={`h-full transition-all duration-500 ease-out overflow-hidden ${isAttachmentViewerOpen ? 'w-[70%]' : 'w-0'
                                }`}
                        >
                            <div className="w-full h-full">
                                {selectedAttachment && (
                                    <AttachmentViewer
                                        isDarkMode={isDarkMode}
                                        onClose={handleCloseAttachmentViewer}
                                        content={selectedAttachment}
                                    />
                                )}
                            </div>
                        </div>
                        <div className={`flex flex-col h-full transition-all duration-500 ease-out ${isAttachmentViewerOpen ? 'w-[30%]' : 'w-full'
                            }`}>
                            {/* 对话历史 - 占据剩余空间并内部滚动 */}
                            <div className="flex-1 min-h-0">
                                <ChatHistory
                                    messages={messages}
                                    isLoading={isLoading || isHistoryLoading}
                                    isDarkMode={isDarkMode}
                                    streamingMessageId={streamingMessageId}
                                    onSuggestionClick={handleSuggestionClick}
                                    userInfo={userInfo}
                                    onAttachmentClick={handleAttachmentClick}
                                    onRegenerate={handleRegenerate}
                                />
                            </div>

                            {/* 输入框 - 固定在底部 */}
                            <div className="flex-shrink-0">
                                <ChatInput
                                    value={inputValue}
                                    onChange={setInputValue}
                                    onSend={handleSendMessage}
                                    isLoading={isLoading || isCreatingAgentChat}
                                    isDarkMode={isDarkMode}
                                    placeholder="继续对话..."
                                    isWelcomeMode={false}
                                    onModeChange={handleModeChange}
                                    onToggleSettings={() => setIsSettingsDrawerOpen(true)}
                                    sessionId={sessionId}
                                    sessionMetadata={sessionMetadata}
                                    onUpdateSessionMetadata={handleSaveSettings}
                                />
                            </div>
                        </div>
                    </div>
                )}
            </main>
            <SessionSettingsDrawer
                isOpen={isSettingsDrawerOpen}
                onClose={() => setIsSettingsDrawerOpen(false)}
                metadata={sessionMetadata}
                isDarkMode={isDarkMode}
                onSave={handleSaveSettings}
                token={token || undefined}
            />
        </div>
    );
};

export default ChatInterface; 