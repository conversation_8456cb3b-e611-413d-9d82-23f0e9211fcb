import OpenAI from 'openai';

// API 基础配置 - 直接使用后端地址
//export const API_BASE_URL = 'http://ai.kumhosunny.cn/v1/';

export const API_BASE_URL = 'http://192.168.120.26:3000/v1/';

// 创建 OpenAI 客户端实例
export const openai = new OpenAI({
    baseURL: `/v1`,
    apiKey: process.env.REACT_APP_OPENAI_API_KEY || 'dummy-key',
    dangerouslyAllowBrowser: true, // 注意：在生产环境中应该通过后端代理调用
});

// OpenAI API 类型定义
export interface ChatMessage {
    role: 'system' | 'user' | 'assistant';
    content: string;
}

export interface ChatCompletionRequest {
    messages: ChatMessage[];
    model?: string;
    temperature?: number;
    max_tokens?: number;
    tools?: Array<Record<string, any>>; // 支持工具调用参数，格式为 List<Map<String, Object>>
    sessionId?: string;
    knowledgeEnabled?: boolean; // 知识库开关
    webSearchEnabled?: boolean; // 联网查询开关
}