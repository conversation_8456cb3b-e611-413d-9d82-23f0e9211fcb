import React, { useRef, useState } from 'react';
import { Upload, X, Image as ImageIcon, Loader2 } from 'lucide-react';
import { uploadFiles } from '../utils/api';
import { useAuth } from '../hooks';
import { UploadedFileDetail } from '../types';
import { toast } from 'react-hot-toast';

interface ImageUploadProps {
    onImageUploaded: (imageUrl: string) => void;
    isDarkMode: boolean;
    disabled?: boolean;
    currentImage?: string;
    onRemoveImage?: () => void;
}

const ImageUpload: React.FC<ImageUploadProps> = ({
    onImageUploaded,
    isDarkMode,
    disabled = false,
    currentImage,
    onRemoveImage
}) => {
    const fileInputRef = useRef<HTMLInputElement>(null);
    const [isDragging, setIsDragging] = useState(false);
    const [isUploading, setIsUploading] = useState(false);
    const { token } = useAuth();

    const handleUpload = async (files: File[]) => {
        if (files.length === 0 || disabled) return;

        // 只处理第一个文件，并且必须是图片
        const file = files[0];
        if (!file.type.startsWith('image/')) {
            toast.error('请选择图片文件', {
                style: {
                    background: isDarkMode ? '#374151' : '#fff',
                    color: isDarkMode ? '#fff' : '#374151',
                },
            });
            return;
        }

        setIsUploading(true);
        try {
            const response = await uploadFiles({ type: 'personal', files: [file] }, token || undefined);
            if (response.code === 200 && response.data && response.data.length > 0) {
                const uploadedFile: UploadedFileDetail = response.data[0];
                onImageUploaded(uploadedFile.path);
                toast.success('图片上传成功', {
                    style: {
                        background: isDarkMode ? '#374151' : '#fff',
                        color: isDarkMode ? '#fff' : '#374151',
                    },
                });
            } else {
                throw new Error(response.message || 'Upload failed');
            }
        } catch (error: any) {
            console.error("Failed to upload image:", error);
            toast.error(error.message || '图片上传失败', {
                style: {
                    background: isDarkMode ? '#374151' : '#fff',
                    color: isDarkMode ? '#fff' : '#374151',
                },
            });
        } finally {
            setIsUploading(false);
        }
    };

    const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(false);
        if (disabled) return;
        
        const files = Array.from(e.dataTransfer.files);
        handleUpload(files);
    };

    const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.stopPropagation();
        if (!disabled) {
            setIsDragging(true);
        }
    };

    const handleDragEnter = (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.stopPropagation();
        if (!disabled) {
            setIsDragging(true);
        }
    };

    const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(false);
    };

    const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && !disabled) {
            handleUpload(Array.from(e.target.files));
        }
    };

    const handleClick = () => {
        if (!disabled && fileInputRef.current) {
            fileInputRef.current.click();
        }
    };

    const theme = {
        background: isDarkMode ? 'bg-gray-800' : 'bg-gray-50',
        border: isDarkMode ? 'border-gray-600' : 'border-gray-300',
        text: isDarkMode ? 'text-gray-300' : 'text-gray-600',
        textSecondary: isDarkMode ? 'text-gray-400' : 'text-gray-500',
        hover: isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100',
        dragActive: isDarkMode ? 'bg-blue-900/20 border-blue-500' : 'bg-blue-50 border-blue-400',
    };

    if (currentImage) {
        return (
            <div className="relative">
                <img
                    src={currentImage}
                    alt="上传的图片"
                    className="w-full h-48 object-cover rounded-lg border-2 border-gray-300"
                />
                {onRemoveImage && !disabled && (
                    <button
                        onClick={onRemoveImage}
                        className={`absolute top-2 right-2 p-1 rounded-full ${isDarkMode ? 'bg-gray-800 text-white hover:bg-gray-700' : 'bg-white text-gray-600 hover:bg-gray-100'} shadow-lg transition-colors`}
                        title="移除图片"
                    >
                        <X size={16} />
                    </button>
                )}
            </div>
        );
    }

    return (
        <div
            className={`relative border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-all duration-200 ${
                isDragging ? theme.dragActive : `${theme.background} ${theme.border} ${theme.hover}`
            } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onDragEnter={handleDragEnter}
            onDragLeave={handleDragLeave}
            onClick={handleClick}
        >
            <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                style={{ display: 'none' }}
                onChange={handleFileInputChange}
                disabled={disabled}
            />
            
            <div className="flex flex-col items-center space-y-3">
                {isUploading ? (
                    <>
                        <Loader2 size={32} className={`animate-spin ${theme.text}`} />
                        <p className={theme.text}>上传中...</p>
                    </>
                ) : (
                    <>
                        <div className={`p-3 rounded-full ${isDarkMode ? 'bg-gray-700' : 'bg-gray-200'}`}>
                            {isDragging ? (
                                <Upload size={24} className="text-blue-500" />
                            ) : (
                                <ImageIcon size={24} className={theme.text} />
                            )}
                        </div>
                        <div>
                            <p className={theme.text}>
                                {isDragging ? '释放以上传图片' : '点击或拖拽图片到此处'}
                            </p>
                            <p className={`text-sm ${theme.textSecondary}`}>
                                支持 JPG、PNG、GIF 等格式
                            </p>
                        </div>
                    </>
                )}
            </div>
        </div>
    );
};

export default ImageUpload;
