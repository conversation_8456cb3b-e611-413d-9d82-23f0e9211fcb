import React, { useRef, useEffect, useState } from 'react';
import { useAuth } from '../../hooks';
import { uploadFiles } from '../../utils/api';
import { UploadedFileDetail } from '../../types';

/**
 * ChatInput 组件 - 支持 Chat 和 Agent 模式切换
 * 
 * 🚨 注意：Agent功能已临时禁用，当前只支持Chat模式
 * 
 * 模式说明:
 * - Chat 对话模式: 与 AI 模型进行直接对话和推理交流
 *   适用场景: 问答咨询、创意写作、文本分析、学习辅导、头脑风暴等纯文本交互
 *   特点: 快速响应、专注对话、无外部依赖
 * 
 * - Agent 智能体模式: AI 可调用外部工具和 API 来完成复杂任务 [已临时禁用]
 *   适用场景: 网络搜索、代码执行、文件处理、数据分析、API 调用、自动化任务等
 *   工具能力: 搜索引擎、计算器、文件操作、图像处理、邮件发送、数据库查询等
 *   特点: 功能强大、可扩展、支持多步骤任务执行
 * 
 * 使用示例:
 * ```tsx
 * const [chatMode, setChatMode] = useState<'chat' | 'agent'>('chat');
 * 
 * const handleSend = () => {
 *   const request: ChatCompletionRequest = {
 *     model: "gpt-3.5-turbo",
 *     messages: [...],
 *     tools: chatMode === 'agent' ? { agent: true } : undefined
 *   };
 *   // 发送请求...
 * };
 * 
 * <ChatInput
 *   value={inputValue}
 *   onChange={setInputValue}
 *   onSend={handleSend}
 *   onModeChange={setChatMode}
 *   // ... 其他 props
 * />
 * ```
 */

interface Attachment {
    id: string;
    file: File;
    status: 'uploading' | 'completed' | 'error';
    fileInfo?: UploadedFileDetail;
    error?: string;
}

interface ChatInputProps {
    value: string;
    onChange: (value: string) => void;
    onSend: (content: any[], text: string, attachments: UploadedFileDetail[]) => void;
    isLoading: boolean;
    isDarkMode: boolean;
    placeholder?: string;
    isWelcomeMode?: boolean;
    onModeChange?: (mode: 'chat' | 'agent') => void;
    onToggleSettings?: () => void;
    sessionId?: string; // 添加sessionId参数用于监听会话切换
    sessionMetadata?: any; // 添加会话元数据参数
    onUpdateSessionMetadata?: (metadata: any) => void; // 添加更新会话元数据的回调
}

const ChatInput: React.FC<ChatInputProps> = ({
    value,
    onChange,
    onSend,
    isLoading,
    isDarkMode,
    placeholder = "输入您的问题...",
    isWelcomeMode = false,
    onModeChange,
    onToggleSettings,
    sessionId,
    sessionMetadata,
    onUpdateSessionMetadata,
}) => {
    const textareaRef = useRef<HTMLTextAreaElement>(null);
    const fileInputRef = useRef<HTMLInputElement>(null);
    const [selectedMode, setSelectedMode] = useState<'chat' | 'agent'>('chat');
    const [isDropdownOpen, setIsDropdownOpen] = useState(false);
    const [attachments, setAttachments] = useState<Attachment[]>([]);
    const [isDragging, setIsDragging] = useState(false);
    const { token } = useAuth();

    // 从会话metadata中读取开关状态
    const isKnowledgeEnabled = sessionMetadata?.settings?.knowledgeEnabled || false;
    const isWebSearchEnabled = sessionMetadata?.settings?.webSearchEnabled || false;

    const isUploading = attachments.some(a => a.status === 'uploading');

    const adjustTextareaHeight = () => {
        if (textareaRef.current) {
            textareaRef.current.style.height = 'auto';
            textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
        }
    };

    useEffect(() => {
        adjustTextareaHeight();
    }, [value]);

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            const target = event.target as HTMLElement;
            if (!target.closest('.mode-dropdown')) {
                setIsDropdownOpen(false);
            }
        };

        if (isDropdownOpen) {
            document.addEventListener('mousedown', handleClickOutside);
        }

        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [isDropdownOpen]);

    // 监听sessionId变化，重置组件状态
    useEffect(() => {
        // 重置ChatInput的状态
        setSelectedMode('chat');
        setIsDropdownOpen(false);
        setAttachments([]);
        setIsDragging(false);
        // 注意：开关状态现在从sessionMetadata中读取，不需要重置
    }, [sessionId]);

    const handleUpload = (files: File[]) => {
        if (files.length === 0) return;

        const newAttachments: Attachment[] = files.map(file => ({
            id: `${file.name}-${Date.now()}`,
            file,
            status: 'uploading',
        }));

        setAttachments(prev => [...prev, ...newAttachments]);

        uploadFiles({ type: 'personal', files }, token || undefined)
            .then(response => {
                if (response.code === 200 && response.data) {
                    const uploadedFilesData: UploadedFileDetail[] = response.data;
                    setAttachments(prev => {
                        const updatedAttachments = [...prev];
                        uploadedFilesData.forEach(fileInfo => {
                            const matchingAttachmentIndex = updatedAttachments.findIndex(
                                a => a.file.name === fileInfo.originalFileName && a.status === 'uploading'
                            );
                            if (matchingAttachmentIndex !== -1) {
                                updatedAttachments[matchingAttachmentIndex] = {
                                    ...updatedAttachments[matchingAttachmentIndex],
                                    status: 'completed',
                                    fileInfo: fileInfo,
                                };
                            }
                        });
                        return updatedAttachments;
                    });
                } else {
                    throw new Error(response.message || 'Upload failed');
                }
            }).catch(error => {
                setAttachments(prev => prev.map(a => {
                    const isNew = newAttachments.some(na => na.id === a.id);
                    if (isNew) {
                        return { ...a, status: 'error', error: error.message || 'Unknown error' };
                    }
                    return a;
                }));
            });
    };

    const handlePaste = (e: React.ClipboardEvent<HTMLTextAreaElement>) => {
        const files = Array.from(e.clipboardData.files);
        if (files.length > 0) {
            e.preventDefault();
            handleUpload(files);
        }
    };

    const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(false);
        const files = Array.from(e.dataTransfer.files);
        if (files.length > 0) {
            handleUpload(files);
        }
    };

    const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(true);
    };

    const handleDragEnter = (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(true);
    };

    const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(false);
    };

    const removeAttachment = (idToRemove: string) => {
        setAttachments(prev => prev.filter((a) => a.id !== idToRemove));
    };

    const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
        onChange(e.target.value);
        adjustTextareaHeight();
    };

    const handleModeSelect = (mode: 'chat' | 'agent') => {
        // Agent功能已临时禁用，强制使用chat模式
        const forcedMode = 'chat';
        setSelectedMode(forcedMode);
        setIsDropdownOpen(false);
        onModeChange?.(forcedMode);
    };

    const handleKeyPress = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            handleSend();
        }
    };

    // 切换知识库开关
    const handleToggleKnowledge = () => {
        if (!onUpdateSessionMetadata || !sessionMetadata) return;

        const newMetadata = {
            ...sessionMetadata,
            settings: {
                ...sessionMetadata.settings,
                knowledgeEnabled: !isKnowledgeEnabled,
            },
        };
        onUpdateSessionMetadata(newMetadata);
    };

    // 切换联网查询开关
    const handleToggleWebSearch = () => {
        if (!onUpdateSessionMetadata || !sessionMetadata) return;

        const newMetadata = {
            ...sessionMetadata,
            settings: {
                ...sessionMetadata.settings,
                webSearchEnabled: !isWebSearchEnabled,
            },
        };
        onUpdateSessionMetadata(newMetadata);
    };

    const handleSend = () => {
        if (isLoading || isUploading) return;

        const content: any[] = [];
        if (value.trim()) {
            content.push({
                type: 'text',
                text: value.trim(),
            });
        }

        const uploadedAttachments = attachments.filter(a => a.status === 'completed' && a.fileInfo);
        const uploadedImages = uploadedAttachments.filter(a => a.file.type.startsWith('image/'));

        if (uploadedImages.length > 0) {
            uploadedImages.forEach(img => {
                content.push({
                    type: 'image_url',
                    image_url: { url: img.fileInfo!.path },
                });
            });
        }

        if (content.length > 0 || uploadedAttachments.length > 0) {
            onSend(content, value, uploadedAttachments.map(a => a.fileInfo!));
            setAttachments([]);
        }
    };

    const themeClasses = {
        background: isDarkMode ? 'bg-gray-900' : 'bg-white',
        text: isDarkMode ? 'text-white' : 'text-gray-900',
        textSecondary: isDarkMode ? 'text-gray-400' : 'text-gray-500',
        placeholder: isDarkMode ? 'placeholder-gray-500' : 'placeholder-gray-400',
        inputBg: isDarkMode ? 'bg-gray-800' : 'bg-white',
        border: isDarkMode ? 'border-gray-700' : 'border-gray-200',
        toolbarBg: isDarkMode ? 'bg-gray-850' : 'bg-gray-25',
        toolbarBorder: isDarkMode ? 'border-gray-600' : 'border-gray-100',
        buttonBg: isDarkMode ? 'bg-gray-700' : 'bg-white',
        buttonHover: isDarkMode ? 'hover:bg-gray-600' : 'hover:bg-gray-100',
        buttonActive: isDarkMode ? 'active:bg-gray-500' : 'active:bg-gray-200',
        kbdBg: isDarkMode ? 'bg-gray-700' : 'bg-gray-100',
        kbdText: isDarkMode ? 'text-gray-300' : 'text-gray-600',
        kbdBorder: isDarkMode ? 'border-gray-600' : 'border-gray-300',
        dropdownBg: isDarkMode ? 'bg-gray-800' : 'bg-white',
        dropdownBorder: isDarkMode ? 'border-gray-600' : 'border-gray-200',
        dropdownHover: isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-50',
    };

    return (
        <div className={`${themeClasses.background} ${isWelcomeMode ? '' : 'px-6 py-4'}`}>
            <div className="max-w-4xl mx-auto w-full">
                <div className="relative">
                    {/* 主输入框容器 */}
                    <div className={`${themeClasses.inputBg} rounded-xl transition-all duration-200 ${isDragging ? 'ring-2 ring-blue-500' : ''}`}
                        style={{
                            boxShadow: isDarkMode
                                ? '0 4px 20px rgba(0, 0, 0, 0.2), 0 2px 8px rgba(0, 0, 0, 0.1)'
                                : '0 4px 20px rgba(0, 0, 0, 0.06), 0 2px 8px rgba(0, 0, 0, 0.03)',
                        }}
                        onFocus={(e) => {
                            e.currentTarget.style.boxShadow = isDarkMode
                                ? '0 8px 30px rgba(0, 0, 0, 0.25), 0 4px 12px rgba(0, 0, 0, 0.15)'
                                : '0 8px 30px rgba(0, 0, 0, 0.08), 0 4px 12px rgba(0, 0, 0, 0.04)';
                        }}
                        onBlur={(e) => {
                            e.currentTarget.style.boxShadow = isDarkMode
                                ? '0 4px 20px rgba(0, 0, 0, 0.2), 0 2px 8px rgba(0, 0, 0, 0.1)'
                                : '0 4px 20px rgba(0, 0, 0, 0.06), 0 2px 8px rgba(0, 0, 0, 0.03)';
                        }}
                        onMouseEnter={(e) => {
                            if (document.activeElement !== e.currentTarget.querySelector('textarea')) {
                                e.currentTarget.style.boxShadow = isDarkMode
                                    ? '0 6px 25px rgba(0, 0, 0, 0.22), 0 3px 10px rgba(0, 0, 0, 0.12)'
                                    : '0 6px 25px rgba(0, 0, 0, 0.07), 0 3px 10px rgba(0, 0, 0, 0.035)';
                            }
                        }}
                        onMouseLeave={(e) => {
                            if (document.activeElement !== e.currentTarget.querySelector('textarea')) {
                                e.currentTarget.style.boxShadow = isDarkMode
                                    ? '0 4px 20px rgba(0, 0, 0, 0.2), 0 2px 8px rgba(0, 0, 0, 0.1)'
                                    : '0 4px 20px rgba(0, 0, 0, 0.06), 0 2px 8px rgba(0, 0, 0, 0.03)';
                            }
                        }}
                        onDrop={handleDrop}
                        onDragOver={handleDragOver}
                        onDragEnter={handleDragEnter}
                        onDragLeave={handleDragLeave}
                    >
                        {/* 附件上传 */}
                        <input
                            ref={fileInputRef}
                            type="file"
                            multiple
                            accept="*"
                            style={{ display: 'none' }}
                            onChange={(e) => {
                                if (e.target.files) {
                                    handleUpload(Array.from(e.target.files));
                                }
                            }}
                        />
                        {/* 输入框 */}
                        <textarea
                            ref={textareaRef}
                            value={value}
                            onChange={handleChange}
                            onKeyPress={handleKeyPress}
                            onPaste={handlePaste}
                            placeholder={placeholder}
                            className={`w-full bg-transparent ${themeClasses.text} ${themeClasses.placeholder} border-none outline-none resize-none py-4 px-4 text-base leading-relaxed`}
                            rows={1}
                            style={{
                                minHeight: '80px',
                                maxHeight: '300px'
                            }}
                        />

                        {isDragging && (
                            <div className="absolute inset-0 bg-blue-500/10 rounded-xl flex items-center justify-center pointer-events-none z-10">
                                <div className="text-center text-blue-500">
                                    <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" /></svg>
                                    <p className="mt-2 font-semibold">拖动文件到这里上传</p>
                                </div>
                            </div>
                        )}

                        {/* 附件预览 */}
                        {attachments.length > 0 && (
                            <div className={`px-4 pt-2 pb-1 border-t ${themeClasses.toolbarBorder}`}>
                                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-3">
                                    {attachments.map((attachment) => (
                                        <div key={attachment.id} className={`relative group p-2 rounded-lg text-xs flex items-center space-x-2 ${themeClasses.buttonBg} shadow-sm overflow-hidden`}>
                                            {attachment.status === 'completed' && attachment.fileInfo && attachment.file.type.startsWith('image/') ? (
                                                <img src={attachment.fileInfo.path} alt={attachment.file.name} className="w-8 h-8 object-cover rounded" />
                                            ) : (
                                                <div className="w-8 h-8 flex-shrink-0 flex items-center justify-center bg-gray-200 dark:bg-gray-700 rounded">
                                                    <svg className="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" /></svg>
                                                </div>
                                            )}
                                            <div className="flex-1 truncate">
                                                <span className={`block truncate ${themeClasses.text}`}>{attachment.file.name}</span>
                                                {attachment.status === 'uploading' && <span className="text-xs text-blue-500">上传中...</span>}
                                                {attachment.status === 'completed' && <span className="text-xs text-green-500">上传成功</span>}
                                                {attachment.status === 'error' && <span className="text-xs text-red-500" title={attachment.error}>上传失败</span>}
                                            </div>

                                            {attachment.status === 'uploading' && (
                                                <div className="absolute inset-0 bg-black/30 flex items-center justify-center">
                                                    <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                                    </svg>
                                                </div>
                                            )}

                                            <button
                                                onClick={() => removeAttachment(attachment.id)}
                                                className={`absolute -top-1.5 -right-1.5 w-5 h-5 flex items-center justify-center ${themeClasses.kbdBg} text-red-500 rounded-full opacity-0 group-hover:opacity-100 transition-opacity hover:bg-red-500 hover:text-white z-10`}
                                            >
                                                <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20"><path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" /></svg>
                                            </button>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        )}

                        {/* 底部工具栏 */}
                        <div className={`${themeClasses.toolbarBg} px-4 py-3 rounded-b-xl flex items-center justify-between backdrop-blur-sm`}>
                            {/* 左侧工具按钮 */}
                            <div className="flex items-center space-x-2">
                                {/* 模式选择下拉框 */}
                                <div className="relative mode-dropdown">
                                    <button
                                        onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                                        className={`group relative flex items-center space-x-2 px-3 py-2 ${themeClasses.textSecondary} hover:${themeClasses.text} ${themeClasses.buttonBg} ${themeClasses.buttonHover} ${themeClasses.buttonActive} rounded-lg transition-all duration-200 shadow-sm hover:shadow-md text-sm`}
                                    >
                                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            {/* Agent功能已禁用，始终显示chat图标 */}
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                        </svg>
                                        <span className="capitalize">chat</span>
                                        <svg className={`w-3 h-3 transition-transform ${isDropdownOpen ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                        </svg>
                                    </button>

                                    {/* 下拉菜单 - Agent功能已临时禁用 */}
                                    {isDropdownOpen && (
                                        <div className={`absolute bottom-full left-0 mb-2 ${themeClasses.dropdownBg} ${themeClasses.dropdownBorder} border rounded-lg shadow-lg z-50 min-w-[120px]`}>
                                            <button
                                                onClick={() => handleModeSelect('chat')}
                                                className={`w-full flex items-center space-x-2 px-3 py-2 text-sm ${themeClasses.text} ${themeClasses.dropdownHover} transition-colors ${selectedMode === 'chat' ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400' : ''} rounded-lg`}
                                            >
                                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                                </svg>
                                                <span>Chat</span>
                                                {selectedMode === 'chat' && (
                                                    <svg className="w-3 h-3 ml-auto text-blue-600 dark:text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                                    </svg>
                                                )}
                                            </button>
                                            {/* Agent选项已临时禁用
                                            <button
                                                onClick={() => handleModeSelect('agent')}
                                                className={`w-full flex items-center space-x-2 px-3 py-2 text-sm ${themeClasses.text} ${themeClasses.dropdownHover} transition-colors ${selectedMode === 'agent' ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400' : ''} last:rounded-b-lg`}
                                            >
                                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                                                </svg>
                                                <span>Agent</span>
                                                {selectedMode === 'agent' && (
                                                    <svg className="w-3 h-3 ml-auto text-blue-600 dark:text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                                    </svg>
                                                )}
                                            </button>
                                            */}
                                        </div>
                                    )}
                                </div>

                                <button onClick={onToggleSettings} className={`group relative p-2.5 ${themeClasses.textSecondary} hover:${themeClasses.text} ${themeClasses.buttonBg} ${themeClasses.buttonHover} ${themeClasses.buttonActive} rounded-lg transition-all duration-200 shadow-sm hover:shadow-md`} title="会话设置">
                                    <svg className="w-4 h-4 transition-transform group-hover:scale-110" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth="1.5">
                                        <path strokeLinecap="round" strokeLinejoin="round" d="M9.594 3.94c.09-.542.56-.94 1.11-.94.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 011.37.49l1.296 2.247a1.125 1.125 0 01-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 010 .255c-.007.378.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 01-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 01-.22.128c-.331.183-.581.495-.644.869l-.213 1.28c-.09.543-.56.941-1.11.941-.55 0-1.02-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 01-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 01-1.369-.49l-1.297-2.247a1.125 1.125 0 01.26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.932 6.932 0 010-.255c.007-.378-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 01-.26-1.43l1.297-2.247a1.125 1.125 0 011.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.087.22-.128.332-.183.582-.495.644-.869l.214-1.281z" />
                                        <path strokeLinecap="round" strokeLinejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                    </svg>
                                    <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-gray-900 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
                                        会话设置
                                    </div>
                                </button>

                                <button onClick={() => fileInputRef.current?.click()} className={`group relative p-2.5 ${themeClasses.textSecondary} hover:${themeClasses.text} ${themeClasses.buttonBg} ${themeClasses.buttonHover} ${themeClasses.buttonActive} rounded-lg transition-all duration-200 shadow-sm hover:shadow-md`} title="添加附件">
                                    <svg className="w-4 h-4 transition-transform group-hover:scale-110" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                                    </svg>
                                    <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-gray-900 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
                                        添加附件
                                    </div>
                                </button>

                                {/* 语音输入按钮已隐藏 */}
                                {/* <button className={`group relative p-2.5 ${themeClasses.textSecondary} hover:${themeClasses.text} ${themeClasses.buttonBg} ${themeClasses.buttonHover} ${themeClasses.buttonActive} rounded-lg transition-all duration-200 shadow-sm hover:shadow-md`} title="语音输入">
                                    <svg className="w-4 h-4 transition-transform group-hover:scale-110" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                                    </svg>
                                    <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-gray-900 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
                                        语音输入
                                    </div>
                                </button> */}

                                <button onClick={handleToggleKnowledge} className={`group relative p-2.5 ${isKnowledgeEnabled ? 'text-blue-600 dark:text-blue-400' : themeClasses.textSecondary} hover:${themeClasses.text} ${isKnowledgeEnabled ? 'bg-blue-100 dark:bg-blue-900/30 shadow-md' : themeClasses.buttonBg} ${themeClasses.buttonHover} ${themeClasses.buttonActive} rounded-lg transition-all duration-200 shadow-sm hover:shadow-md ${isKnowledgeEnabled ? 'ring-2 ring-blue-200 dark:ring-blue-800' : ''}`} title="知识库">
                                    <svg className="w-4 h-4 transition-transform group-hover:scale-110" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 6.042A8.967 8.967 0 006 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 016 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 016-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0018 18a8.967 8.967 0 00-6 2.292m0-14.25v14.25" />
                                    </svg>
                                    <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-gray-900 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
                                        知识库
                                    </div>
                                </button>

                                <button onClick={handleToggleWebSearch} className={`group relative p-2.5 ${isWebSearchEnabled ? 'text-green-600 dark:text-green-400' : themeClasses.textSecondary} hover:${themeClasses.text} ${isWebSearchEnabled ? 'bg-green-100 dark:bg-green-900/30 shadow-md' : themeClasses.buttonBg} ${themeClasses.buttonHover} ${themeClasses.buttonActive} rounded-lg transition-all duration-200 shadow-sm hover:shadow-md ${isWebSearchEnabled ? 'ring-2 ring-green-200 dark:ring-green-800' : ''}`} title="联网查询">
                                    <svg className="w-4 h-4 transition-transform group-hover:scale-110" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z" />
                                    </svg>
                                    <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-gray-900 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
                                        联网查询
                                    </div>
                                </button>
                            </div>

                            {/* 右侧信息区域 */}
                            <div className="flex items-center space-x-4">
                                {value.length > 0 && (
                                    <div className={`${themeClasses.textSecondary} text-xs flex items-center space-x-1`}>
                                        <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        <span>{value.length}</span>
                                    </div>
                                )}
                                <div className={`${themeClasses.textSecondary} text-xs flex items-center space-x-2`}>
                                    <div className="flex items-center space-x-1">
                                        <kbd className={`px-2 py-1 text-xs ${themeClasses.kbdBg} ${themeClasses.kbdText} rounded-md font-mono shadow-sm`}>Shift</kbd>
                                        <kbd className={`px-2 py-1 text-xs ${themeClasses.kbdBg} ${themeClasses.kbdText} rounded-md font-mono shadow-sm`}>↵</kbd>
                                    </div>
                                    <span className="text-xs">换行</span>
                                    <div className="flex items-center space-x-1">
                                        <kbd className={`px-2 py-1 text-xs ${themeClasses.kbdBg} ${themeClasses.kbdText} rounded-md font-mono shadow-sm`}>↵</kbd>
                                    </div>
                                    <span className="text-xs">发送</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ChatInput; 