import React, { useState, useEffect } from 'react';
import { useTheme } from '../common/useTheme';
import { AttachmentContent } from '../common/types';

interface PptViewerProps {
    isDarkMode: boolean;
    content: AttachmentContent;
}

const PptViewer: React.FC<PptViewerProps> = ({ isDarkMode, content }) => {
    const themeColors = useTheme(isDarkMode);
    const [viewMode, setViewMode] = useState<'office' | 'download'>('office');
    const [isLoading, setIsLoading] = useState(true);
    const [hasError, setHasError] = useState(false);

    // 构建 Office Online 查看器 URL
    const getOfficeViewerUrl = (fileUrl: string) => {
        // 使用 Microsoft Office Online 查看器
        return `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(fileUrl)}`;
    };

    // 处理 iframe 加载
    const handleIframeLoad = () => {
        setIsLoading(false);
    };

    // 处理 iframe 错误
    const handleIframeError = () => {
        setIsLoading(false);
        setHasError(true);
        setViewMode('download');
    };

    useEffect(() => {
        // 重置状态当内容改变时
        setIsLoading(true);
        setHasError(false);
        setViewMode('office');
    }, [content.url]);

    // 渲染 Office Online 查看器
    const renderOfficeViewer = () => {
        if (!content.url) return null;

        return (
            <div className="relative h-full w-full">
                {isLoading && (
                    <div className="absolute inset-0 flex items-center justify-center" style={{ backgroundColor: themeColors.contentBg }}>
                        <div className="text-center">
                            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
                            <p style={{ color: themeColors.secondaryText }}>正在加载 PPT 预览...</p>
                        </div>
                    </div>
                )}
                <iframe
                    src={getOfficeViewerUrl(content.url)}
                    className="w-full h-full border-0"
                    onLoad={handleIframeLoad}
                    onError={handleIframeError}
                    title={`PPT 预览 - ${content.title}`}
                    sandbox="allow-scripts allow-same-origin allow-popups allow-forms"
                />
            </div>
        );
    };

    // 渲染下载模式
    const renderDownloadMode = () => (
        <div className="h-full flex flex-col items-center justify-center mx-4 my-4 rounded-lg shadow-sm" style={{ backgroundColor: themeColors.contentBg }}>
            <div className="text-center max-w-md">
                <svg className="w-16 h-16 mx-auto text-orange-500 mb-4" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M4,2H12.5L13,2.5V8.5L12.5,9H11V10.5H12.5L13,11V13.5L12.5,14H4A2,2 0 0,1 2,12V4A2,2 0 0,1 4,2M4,3A1,1 0 0,0 3,4V12A1,1 0 0,0 4,13H12V11.5H10.5L10,11V10H11.5L12,9.5V3H4M14,3.5V8H18.5L14,3.5M14.5,15H20A2,2 0 0,1 22,17V20A2,2 0 0,1 20,22H14.5L14,21.5V15.5L14.5,15M15,16V21H20A1,1 0 0,0 21,20V17A1,1 0 0,0 20,16H15M6,5H8V6H6V5M6,7H10V8H6V7Z" />
                </svg>
                <h3 className="text-lg font-medium mb-2" style={{ color: themeColors.primaryText }}>{content.title}</h3>
                {hasError ? (
                    <p className="mb-4 text-sm" style={{ color: themeColors.secondaryText }}>
                        无法在线预览此 PPT 文件，请下载后查看
                    </p>
                ) : (
                    <p className="mb-4 text-sm" style={{ color: themeColors.secondaryText }}>
                        PowerPoint 演示文稿
                    </p>
                )}

                <div className="space-y-3">
                    {content.url && (
                        <a
                            href={content.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                        >
                            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                            </svg>
                            下载 PPT 文档
                        </a>
                    )}

                    {!hasError && content.url && (
                        <button
                            onClick={() => setViewMode('office')}
                            className="block mx-auto px-4 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                            style={{ color: themeColors.primaryText, borderColor: themeColors.border }}
                        >
                            尝试在线预览
                        </button>
                    )}
                </div>
            </div>
        </div>
    );

    // 渲染工具栏
    const renderToolbar = () => (
        <div className="flex items-center justify-between p-3 border-b" style={{ borderColor: themeColors.border, backgroundColor: themeColors.headerBg }}>
            <div className="flex items-center space-x-2">
                <svg className="w-5 h-5 text-orange-500" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M4,2H12.5L13,2.5V8.5L12.5,9H11V10.5H12.5L13,11V13.5L12.5,14H4A2,2 0 0,1 2,12V4A2,2 0 0,1 4,2M4,3A1,1 0 0,0 3,4V12A1,1 0 0,0 4,13H12V11.5H10.5L10,11V10H11.5L12,9.5V3H4M14,3.5V8H18.5L14,3.5M14.5,15H20A2,2 0 0,1 22,17V20A2,2 0 0,1 20,22H14.5L14,21.5V15.5L14.5,15M15,16V21H20A1,1 0 0,0 21,20V17A1,1 0 0,0 20,16H15M6,5H8V6H6V5M6,7H10V8H6V7Z" />
                </svg>
                <span className="font-medium text-sm" style={{ color: themeColors.primaryText }}>
                    {content.title}
                </span>
            </div>

            <div className="flex items-center space-x-2">
                {viewMode === 'office' && !hasError && (
                    <button
                        onClick={() => setViewMode('download')}
                        className="px-3 py-1 text-xs rounded border hover:bg-gray-50 transition-colors"
                        style={{ color: themeColors.secondaryText, borderColor: themeColors.border }}
                    >
                        下载模式
                    </button>
                )}
                {content.url && (
                    <a
                        href={content.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                    >
                        下载
                    </a>
                )}
            </div>
        </div>
    );

    return (
        <div className="h-full flex flex-col" style={{ backgroundColor: themeColors.mainBg }}>
            {renderToolbar()}
            <div className="flex-1 overflow-hidden">
                {viewMode === 'office' && !hasError ? renderOfficeViewer() : renderDownloadMode()}
            </div>
        </div>
    );
};

export default PptViewer;