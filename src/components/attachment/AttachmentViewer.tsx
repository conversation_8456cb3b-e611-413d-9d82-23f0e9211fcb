import React from 'react';
import { AttachmentContent } from './common/types';
import { useTheme } from './common/useTheme';
import AttachmentHeader from './common/AttachmentHeader';
import CodeViewer from './viewers/CodeViewer';
import ExcelViewer from './viewers/ExcelViewer';
import WordViewer from './viewers/WordViewer';
import UnsupportedViewer from './viewers/UnsupportedViewer';
import PdfViewer from './viewers/PdfViewer';
import ImageViewer from './viewers/ImageViewer';
import PptViewer from './viewers/PptViewer';

interface AttachmentViewerProps {
    isDarkMode: boolean;
    onClose: () => void;
    content?: AttachmentContent;
}

const AttachmentViewer: React.FC<AttachmentViewerProps> = ({
    isDarkMode,
    onClose,
    content = {
        type: 'code',
        title: 'example.js',
        content: 'console.log("hello world")',
        language: 'javascript'
    }
}) => {
    const themeColors = useTheme(isDarkMode);

    const renderContent = () => {
        if (!content) {
            return <UnsupportedViewer isDarkMode={isDarkMode} />;
        }

        switch (content.type) {
            case 'code':
            case 'text':
                return <CodeViewer isDarkMode={isDarkMode} content={content} />;
            case 'pdf':
                return <PdfViewer isDarkMode={isDarkMode} content={content} />;
            case 'excel':
                return <ExcelViewer isDarkMode={isDarkMode} content={content} />;
            case 'word':
                return <WordViewer isDarkMode={isDarkMode} content={content} />;
            case 'image':
                return <ImageViewer isDarkMode={isDarkMode} content={content} />;
            case 'ppt':
                return <PptViewer isDarkMode={isDarkMode} content={content} />;
            default:
                return <UnsupportedViewer isDarkMode={isDarkMode} />;
        }
    };

    return (
        <div className="h-full flex flex-col" style={{ backgroundColor: themeColors.mainBg }}>
            <AttachmentHeader isDarkMode={isDarkMode} onClose={onClose} content={content} />
            <div className="flex-1 overflow-hidden" style={{ backgroundColor: themeColors.mainBg }}>
                {renderContent()}
            </div>
        </div>
    );
};

export default AttachmentViewer; 